import { AppException, ErrorCode } from '@/common';
import { ApiResponseDto, PaginatedResult } from '@/common/response';
import { S3Service } from '@/shared/services/s3.service';
import { CategoryFolderEnum, FileSizeEnum, generateS3Key, ImageType } from '@/shared/utils';
import { AGENT_ERROR_CODES } from '@modules/agent/exceptions';
import { AgentSystemMcpRepository } from '@modules/agent/repositories/agent-system-mcp.repository';
import { AgentSystemRepository } from '@modules/agent/repositories/agent-system.repository';
import { AgentRepository } from '@modules/agent/repositories/agent.repository';
import { McpSystemsRepository } from '@modules/agent/repositories/mcp-systems.repository';
import { EmployeeInfoService } from '@modules/employee/services/employee-info.service';
import { SystemModelsRepository } from '@modules/models/repositories/system-models.repository';
import { Injectable, Logger } from '@nestjs/common';
import { CdnService } from '@shared/services/cdn.service';
import { TimeIntervalEnum } from '@shared/utils';
import { IsNull } from 'typeorm';
import { Transactional } from 'typeorm-transactional';
import { Agent, AgentSystem } from '../../entities';
import { AgentSystemDetailDto, AgentSystemListItemDto, AgentSystemQueryDto, AgentSystemTrashItemDto, CreateAgentSystemDto, McpSystemDto, SystemModelDto, UpdateAgentSystemDto } from '../dto';

/**
 * Service xử lý logic nghiệp vụ cho Agent System
 */
@Injectable()
export class AdminAgentSystemService {
  private readonly logger = new Logger(AdminAgentSystemService.name);

  constructor(
    private readonly agentSystemRepository: AgentSystemRepository,
    private readonly agentRepository: AgentRepository,
    private readonly agentSystemMcpRepository: AgentSystemMcpRepository,
    private readonly mcpSystemsRepository: McpSystemsRepository,
    private readonly systemModelsRepository: SystemModelsRepository,
    private readonly employeeInfoService: EmployeeInfoService,
    private readonly cdnService: CdnService,
    private readonly s3Service: S3Service,
  ) { }

  /**
   * Lấy danh sách agent system với phân trang
   * @param queryDto Tham số truy vấn
   * @returns Danh sách agent system với phân trang
   */
  async findAll(
    queryDto: AgentSystemQueryDto,
  ): Promise<ApiResponseDto<PaginatedResult<AgentSystemListItemDto>>> {
    try {
      this.logger.log('Getting agent system list with pagination');

      // Lấy danh sách agent system từ repository với thông tin chi tiết
      const result = await this.agentSystemRepository.findPaginatedWithAgentInfo(
        queryDto.page || 1,
        queryDto.limit || 10,
        queryDto.search,
        queryDto.active,
        queryDto.sortBy,
        queryDto.sortDirection,
      );

      // Chuyển đổi kết quả sang DTO
      const items = result.items.map((agentSystemData: any) => {
        return this.mapToListItemDto(agentSystemData);
      });

      const page = queryDto.page || 1;
      const limit = queryDto.limit || 10;

      const paginatedResult = {
        items,
        meta: {
          totalItems: result.total,
          itemCount: items.length,
          itemsPerPage: limit,
          totalPages: Math.ceil(result.total / limit),
          currentPage: page,
          hasItems: result.total > 0,
        },
      };

      return ApiResponseDto.paginated(paginatedResult, 'Lấy danh sách agent system thành công');
    } catch (error) {
      this.logger.error(`Failed to get agent system list: ${error.message}`, error.stack);
      throw new AppException(
        ErrorCode.INTERNAL_SERVER_ERROR,
        'Không thể lấy danh sách agent system'
      );
    }
  }

  /**
   * Map raw result từ join query sang AgentSystemListItemDto
   * @param agentSystemData Raw result từ database join
   * @returns AgentSystemListItemDto
   */
  private mapToListItemDto(agentSystemData: any): AgentSystemListItemDto {
    const dto = new AgentSystemListItemDto();
    dto.id = agentSystemData.id;
    dto.name = agentSystemData.name || 'Unknown Agent';
    dto.nameCode = agentSystemData.namecode || '';
    dto.avatar = agentSystemData.avatar ? this.cdnService.generateUrlView(agentSystemData.avatar, TimeIntervalEnum.ONE_DAY) : null;
    dto.model = agentSystemData.modelid || 'Unknown Model';
    dto.provider = agentSystemData.provider || null;
    dto.active = agentSystemData.active || false;
    dto.isSupervisor = agentSystemData.issupervisor || false;

    return dto;
  }

  /**
   * Lấy thông tin chi tiết agent system theo ID
   * @param id ID của agent system
   * @returns Thông tin chi tiết agent system
   */
  async findById(id: string): Promise<AgentSystemDetailDto> {
    try {
      this.logger.log(`Getting agent system detail with ID: ${id}`);

      // 1. Validate và lấy thông tin cơ bản
      const { agent, agentSystem } = await this.validateAgentSystemExists(id);

      // 2. Lấy thông tin system model
      const systemModel = await this.getSystemModelInfo(agentSystem.systemModelId);

      // 3. Lấy danh sách MCP systems
      const mcpSystems = await this.getMcpSystemsInfo(id);

      // 4. Lấy thông tin vector store (nếu có)
      const vectorStore = await this.getVectorStoreInfo(agent.vectorStoreId);

      // 5. Lấy thông tin employees
      const employeeInfo = await this.getEmployeeInfo(agentSystem);

      // 6. Map sang DTO
      return await this.mapToDetailDto(
        agent,
        agentSystem,
        systemModel,
        mcpSystems,
        vectorStore,
        employeeInfo
      );

    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(
        `Error finding agent system by id: ${error.message}`,
        error.stack,
      );
      throw new AppException(AGENT_ERROR_CODES.AGENT_FETCH_FAILED);
    }
  }

  /**
   * Cập nhật thông tin agent system
   * @param id ID của agent system
   * @param updateDto Dữ liệu cập nhật
   * @param employeeId ID của nhân viên cập nhật
   * @returns ID và URL tải lên avatar mới (nếu có)
   */
  @Transactional()
  async update(
    id: string,
    updateDto: UpdateAgentSystemDto,
    employeeId: number,
  ): Promise<{ id: string; avatarUrlUpload?: string }> {
    try {
      this.logger.log(`Updating agent system with ID: ${id}`);

      // 1. Validate agent system exists
      const { agent, agentSystem } = await this.validateAgentSystemExists(id);

      // 2. Validate nameCode uniqueness (nếu có thay đổi)
      if (updateDto.nameCode && updateDto.nameCode !== agentSystem.nameCode) {
        await this.validateUniqueNameCodeForUpdate(updateDto.nameCode, id);
      }

      // 3. Validate supervisor uniqueness (chỉ validate nếu agent system hiện tại là supervisor)
      if (agentSystem.isSupervisor) {
        await this.validateUniqueSupervisorForUpdate(true, id);
      }

      // 4. Validate system model exists (nếu có modelId trong updateDto)
      if (updateDto.modelId) {
        await this.validateSystemModel(updateDto.modelId);
      }

      // 5. Validate vector store exists (nếu có vectorStoreId trong updateDto)
      if (updateDto.vectorStoreId) {
        await this.validateVectorStore(updateDto.vectorStoreId);
      }

      // 6. Validate MCP systems exist (nếu có mcpId array trong updateDto)
      if (updateDto.mcpId && updateDto.mcpId.length > 0) {
        await this.validateMcpSystems(updateDto.mcpId);
      }

      // 7. Update agent table
      let avatarUrlUpload: string | undefined;

      // Cập nhật thông tin agent
      if (updateDto.name) {
        agent.name = updateDto.name;
      }

      if (updateDto.modelConfig) {
        agent.modelConfig = updateDto.modelConfig;
      }

      if (updateDto.instruction !== undefined) {
        agent.instruction = updateDto.instruction;
      }

      // 7.1. Generate avatar upload URL (nếu có avatarMimeType)
      if (updateDto.avatarMimeType) {

        if (!agent.avatar) {
          agent.avatar = generateS3Key({
            baseFolder: employeeId.toString(),
            categoryFolder: CategoryFolderEnum.AGENT,
          });
        }

        avatarUrlUpload = await this.s3Service.createPresignedWithID(
          agent.avatar,
          TimeIntervalEnum.ONE_HOUR,
          ImageType.getType(updateDto.avatarMimeType),
          FileSizeEnum.FIVE_MB,
        );
      }

      // Lưu thay đổi agent
      await this.agentRepository.save(agent);

      // 8. Update agent system table
      if (updateDto.nameCode) {
        agentSystem.nameCode = updateDto.nameCode;
      }

      if (updateDto.description !== undefined) {
        agentSystem.description = updateDto.description;
      }

      if (updateDto.modelId) {
        agentSystem.systemModelId = updateDto.modelId;
      }

      // Set updatedBy
      agentSystem.updatedBy = employeeId;

      // Lưu thay đổi agent system
      await this.agentSystemRepository.save(agentSystem);

      // 9. Handle MCP systems linking (nếu có mcpId trong updateDto)
      if (updateDto.mcpId !== undefined) {
        if (updateDto.mcpId.length > 0) {
          // Xóa các liên kết MCP cũ và tạo liên kết mới
          await this.agentSystemMcpRepository.unlinkAllMcpsFromAgent(id);
          await this.linkAgentWithMcpSystems(id, updateDto.mcpId);
        } else {
          // Nếu mcpId là array rỗng, xóa tất cả liên kết MCP
          await this.agentSystemMcpRepository.unlinkAllMcpsFromAgent(id);
        }
      }

      this.logger.log(`Successfully updated agent system ${id}`, {
        updatedFields: {
          name: !!updateDto.name,
          nameCode: !!updateDto.nameCode,
          modelConfig: !!updateDto.modelConfig,
          instruction: updateDto.instruction !== undefined,
          description: updateDto.description !== undefined,
          modelId: !!updateDto.modelId,
          vectorStoreId: !!updateDto.vectorStoreId,
          avatarMimeType: !!updateDto.avatarMimeType,
          mcpId: !!updateDto.mcpId
        },
        hasAvatarUpload: !!avatarUrlUpload
      });

      return {
        id,
        avatarUrlUpload
      };

    } catch (error) {
      this.logger.error(`Failed to update agent system ${id}: ${error.message}`, error.stack);

      if (error instanceof AppException) {
        throw error;
      }

      throw new AppException(
        AGENT_ERROR_CODES.AGENT_UPDATE_FAILED,
        `Không thể cập nhật agent system: ${error.message}`
      );
    }
  }

  /**
   * Xóa agent system (soft delete)
   * @param id ID của agent system
   * @param employeeId ID của nhân viên xóa
   * @returns ID của agent system đã xóa
   */
  @Transactional()
  async remove(id: string, employeeId: number): Promise<{ id: string }> {
    try {
      this.logger.log(`Removing agent system with ID: ${id}`);

      // Validate agent system exists
      const { agent, agentSystem } = await this.validateAgentSystemExists(id);

      // Cập nhật thông tin xóa cho agent system
      agentSystem.deletedBy = employeeId;
      await this.agentSystemRepository.update({ id }, agentSystem);

      // Xóa mềm agent (set deletedAt)
      agent.deletedAt = Date.now();
      await this.agentRepository.update({ id }, agent);

      this.logger.log(`Successfully removed agent system ${id}`);

      return { id };

    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(
        `Error removing agent system: ${error.message}`,
        error.stack,
      );
      throw new AppException(AGENT_ERROR_CODES.AGENT_DELETE_FAILED);
    }
  }

  /**
   * Xóa nhiều agent systems (soft delete) - Optimized với repository methods
   * @param ids Danh sách ID của agent systems
   * @param employeeId ID của nhân viên xóa
   * @returns Kết quả xóa với danh sách thành công và thất bại
   */
  @Transactional()
  async removes(ids: string[], employeeId: number): Promise<{ deletedIds: string[]; errorIds: string[] }> {
    try {
      this.logger.log(`Bulk removing agent systems: ${ids.join(', ')}`);

      if (!ids || ids.length === 0) {
        return { deletedIds: [], errorIds: [] };
      }

      // 1. Tìm các agent systems tồn tại và chưa bị xóa (sử dụng repository method)
      const existingIds = await this.agentSystemRepository.findExistingIds(ids);
      const errorIds = ids.filter(id => !existingIds.includes(id));

      if (existingIds.length === 0) {
        this.logger.warn(`No valid agent systems found for deletion from provided IDs`);
        return { deletedIds: [], errorIds: ids };
      }

      // 2. Bulk update agents_system table - set deletedBy (sử dụng repository method)
      const agentSystemUpdated = await this.agentSystemRepository.bulkSoftDelete(existingIds, employeeId);

      // 3. Bulk update agents table - set deletedAt (sử dụng repository method)
      const agentsUpdated = await this.agentRepository.bulkSoftDelete(existingIds);

      this.logger.log(`Bulk remove completed. AgentSystems: ${agentSystemUpdated}, Agents: ${agentsUpdated}, Failed: ${errorIds.length}`);

      return {
        deletedIds: existingIds,
        errorIds
      };

    } catch (error) {
      this.logger.error(
        `Error in bulk remove agent systems: ${error.message}`,
        error.stack,
      );
      throw new AppException(AGENT_ERROR_CODES.AGENT_DELETE_FAILED);
    }
  }

  /**
   * Lấy danh sách agent system đã xóa với phân trang - Optimized với repository methods
   * @param queryDto Tham số truy vấn
   * @returns Danh sách agent system đã xóa với phân trang
   */
  async getDeletedAgentSystems(
    queryDto: AgentSystemQueryDto,
  ): Promise<PaginatedResult<AgentSystemTrashItemDto>> {
    try {
      this.logger.log('Getting deleted agent systems list');

      const {
        page = 1,
        limit = 10,
        search,
        sortBy = 'deletedAt',
        sortDirection = 'DESC',
      } = queryDto;

      // 1. Lấy dữ liệu từ repository (sử dụng repository method)
      const { items: rawItems, total, deletedByIds } = await this.agentSystemRepository
        .findDeletedWithDetailsPaginated(page, limit, search, sortBy, sortDirection);

      // 2. Lấy thông tin employees (sử dụng repository method)
      const employeeInfoMap = await this.agentSystemRepository.getEmployeesInfoMap(deletedByIds);

      // 3. Map sang DTO (business logic ở service layer)
      const mappedItems: AgentSystemTrashItemDto[] = rawItems.map((item: any) => ({
        id: item.id,
        name: item.name,
        nameCode: item.nameCode,
        avatar: item.avatar ? this.cdnService.generateUrlView(item.avatar, TimeIntervalEnum.ONE_DAY) : null,
        model: item.modelId || 'Unknown',
        provider: item.provider || null,
        active: item.active,
        deleted: item.deletedBy ? employeeInfoMap.get(item.deletedBy) : undefined
      }));

      // 4. Tạo response với pagination meta
      return {
        items: mappedItems,
        meta: {
          currentPage: page,
          itemsPerPage: limit,
          totalItems: total,
          totalPages: Math.ceil(total / limit),
          itemCount: mappedItems.length
        }
      };

    } catch (error) {
      this.logger.error(`Error fetching deleted agent systems: ${error.message}`, error.stack);
      throw new AppException(AGENT_ERROR_CODES.AGENT_FETCH_FAILED);
    }
  }

  /**
   * Khôi phục nhiều agent systems đã xóa - Optimized với repository methods
   * @param ids Danh sách ID của agent systems cần khôi phục
   * @param employeeId ID của nhân viên thực hiện khôi phục (for logging)
   */
  @Transactional()
  async restoreAgentSystem(ids: string[], employeeId: number): Promise<void> {
    try {
      this.logger.log(`Bulk restoring agent systems: ${ids.join(', ')} by employee ${employeeId}`);

      if (!ids || ids.length === 0) {
        throw new AppException(AGENT_ERROR_CODES.AGENT_SYSTEM_NOT_FOUND, 'Danh sách ID không được để trống');
      }

      // 1. Tìm các agent systems đã xóa và có thể khôi phục (sử dụng repository method)
      const validIds = await this.agentSystemRepository.findDeletedIds(ids);

      if (validIds.length === 0) {
        this.logger.warn(`No valid deleted agent systems found for restoration from provided IDs`);
        return;
      }

      // Log các ID không hợp lệ
      const invalidIds = ids.filter(id => !validIds.includes(id));
      if (invalidIds.length > 0) {
        this.logger.warn(`Invalid or not deleted agent system IDs: ${invalidIds.join(', ')}`);
      }

      // 2. Bulk restore agents_system table - clear deletedBy (sử dụng repository method)
      const agentSystemRestored = await this.agentSystemRepository.bulkRestore(validIds);

      // 3. Bulk restore agents table - clear deletedAt (sử dụng repository method)
      const agentsRestored = await this.agentRepository.bulkRestore(validIds);

      this.logger.log(`Bulk restore completed. AgentSystems: ${agentSystemRestored}, Agents: ${agentsRestored}, Invalid: ${invalidIds.length}`);

    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Error in bulk restore agent systems: ${error.message}`, error.stack);
      throw new AppException(AGENT_ERROR_CODES.AGENT_UPDATE_FAILED);
    }
  }

  /**
   * Toggle trạng thái active của agent system
   * @param id ID của agent system
   * @returns Trạng thái active mới
   */
  async toggleActiveStatus(id: string): Promise<{ id: string; active: boolean }> {
    try {
      this.logger.log(`Toggling active status for agent system: ${id}`);

      // Kiểm tra agent system có tồn tại không
      const exists = await this.agentSystemRepository.existsAndNotDeleted(id);
      if (!exists) {
        throw new AppException(AGENT_ERROR_CODES.AGENT_SYSTEM_NOT_FOUND);
      }

      // Toggle trạng thái active
      const newActiveStatus = await this.agentSystemRepository.toggleActiveStatus(id);

      this.logger.log(`Successfully toggled active status for agent system ${id} to ${newActiveStatus}`);

      return {
        id,
        active: newActiveStatus
      };

    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Error toggling active status: ${error.message}`, error.stack);
      throw new AppException(AGENT_ERROR_CODES.AGENT_UPDATE_FAILED);
    }
  }

  /**
   * Set agent system làm supervisor (chỉ 1 supervisor duy nhất)
   * @param id ID của agent system được set làm supervisor
   */
  async setSupervisor(id: string): Promise<{ id: string; isSupervisor: boolean }> {
    try {
      this.logger.log(`Setting agent system as supervisor: ${id}`);

      // Kiểm tra agent system có tồn tại không
      const exists = await this.agentSystemRepository.existsAndNotDeleted(id);
      if (!exists) {
        throw new AppException(AGENT_ERROR_CODES.AGENT_SYSTEM_NOT_FOUND);
      }

      // Set supervisor (chỉ 1 supervisor duy nhất)
      const affectedRows = await this.agentSystemRepository.setSupervisor(id);

      if (affectedRows === 0) {
        throw new AppException(AGENT_ERROR_CODES.AGENT_UPDATE_FAILED, 'Không thể set agent làm supervisor');
      }

      this.logger.log(`Successfully set agent system ${id} as supervisor`);

      return {
        id,
        isSupervisor: true
      };

    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Error setting supervisor: ${error.message}`, error.stack);
      throw new AppException(AGENT_ERROR_CODES.AGENT_UPDATE_FAILED);
    }
  }

  /**
   * Tạo agent system mới
   * @param createDto Dữ liệu tạo agent system
   * @param employeeId ID của nhân viên tạo
   * @returns ID của agent system đã tạo và URL tải lên avatar (nếu có)
   */
  async create(
    createDto: CreateAgentSystemDto,
    employeeId: number,
  ): Promise<{ id: string; avatarUrlUpload?: string }> {
    try {
      this.logger.log(`Creating agent system with nameCode: ${createDto.nameCode}`);

      // 1. Validate system model exists
      await this.validateSystemModel(createDto.modelId);

      // 2. Validate unique nameCode
      await this.validateUniqueNameCode(createDto.nameCode);

      // 3. Validate unique supervisor (nếu isSupervisor = true)
      if (createDto.isSupervisor) {
        await this.validateUniqueSupervisor();
      }

      // 4. Validate MCP systems exist (nếu có)
      if (createDto.mcpId && createDto.mcpId.length > 0) {
        await this.validateMcpSystems(createDto.mcpId);
      }

      // 5. Tạo agent record trước
      const agent = await this.agentRepository.create({
        name: createDto.name,
        modelConfig: createDto.modelConfig || {},
        instruction: createDto.instruction || '',
        avatar: null, // Sẽ được cập nhật sau khi upload
        createdAt: Date.now(),
        updatedAt: Date.now(),
      });

      // 8. Tạo avatar upload URL (nếu có)
      let avatarUrlUpload: string | undefined;
      if (createDto.avatarMimeType) {

        const key = generateS3Key({
          baseFolder: employeeId.toString(),
          categoryFolder: CategoryFolderEnum.AGENT,
        });

        avatarUrlUpload = await this.s3Service.createPresignedWithID(
          key,
          TimeIntervalEnum.ONE_HOUR,
          ImageType.getType(createDto.avatarMimeType),
          FileSizeEnum.FIVE_MB,
        );

        agent.avatar = key;
      }

      const agentSave = await this.agentRepository.save(agent);

      const agentSystem = await this.agentSystemRepository.create({
        id: agentSave.id,
        nameCode: createDto.nameCode,
        description: createDto.description || undefined,
        isSupervisor: createDto.isSupervisor || false,
        active: true, // Default active
        systemModelId: createDto.modelId,
        createdBy: employeeId,
        updatedBy: employeeId,
      });

      await this.agentSystemRepository.save(agentSystem);

      // 7. Liên kết với MCP systems (nếu có)
      if (createDto.mcpId && createDto.mcpId.length > 0) {
        await this.linkAgentWithMcpSystems(agentSave.id, createDto.mcpId);
      }

      this.logger.log(`Successfully created agent system with ID: ${agentSave.id}`);

      return {
        id: agentSave.id,
        avatarUrlUpload,
      };

    } catch (error) {
      this.logger.error(`Failed to create agent system: ${error.message}`, error.stack);

      if (error instanceof AppException) {
        throw error;
      }

      throw new AppException(
        AGENT_ERROR_CODES.AGENT_CREATION_FAILED,
        `Không thể tạo agent system: ${error.message}`
      );
    }
  }

  /**
   * Validate system model exists
   * @param modelId ID của system model
   */
  private async validateSystemModel(modelId: string): Promise<void> {
    const systemModel = await this.systemModelsRepository.findOne({
      where: { id: modelId }
    });

    if (!systemModel) {
      throw new AppException(AGENT_ERROR_CODES.MODEL_NOT_FOUND);
    }
  }

  /**
   * Validate unique nameCode
   * @param nameCode Name code để kiểm tra
   */
  private async validateUniqueNameCode(nameCode: string): Promise<void> {
    const existingAgentSystem = await this.agentSystemRepository.findOne({
      where: { nameCode, deletedBy: IsNull() }
    });

    if (existingAgentSystem) {
      throw new AppException(AGENT_ERROR_CODES.AGENT_SYSTEM_NAME_CODE_EXISTS);
    }
  }

  /**
   * Validate unique supervisor
   */
  private async validateUniqueSupervisor(): Promise<void> {
    const existingSupervisor = await this.agentSystemRepository.findOne({
      where: { isSupervisor: true, deletedBy: IsNull() }
    });

    if (existingSupervisor) {
      throw new AppException(
        AGENT_ERROR_CODES.AGENT_SYSTEM_NAME_EXISTS, // Sử dụng error code có sẵn
        'Chỉ được có một agent system supervisor'
      );
    }
  }

  /**
   * Validate MCP systems exist
   * @param mcpIds Danh sách MCP system IDs
   */
  private async validateMcpSystems(mcpIds: string[]): Promise<void> {
    for (const mcpId of mcpIds) {
      const mcpSystem = await this.mcpSystemsRepository.findOne({
        where: { id: mcpId }
      });

      if (!mcpSystem) {
        throw new AppException(
          AGENT_ERROR_CODES.AGENT_SYSTEM_NOT_FOUND, // Sử dụng error code có sẵn
          `MCP System với ID ${mcpId} không tồn tại`
        );
      }
    }
  }

  /**
   * Validate agent system exists by ID
   * @param id ID của agent system
   * @returns Agent system entity
   */
  private async validateAgentSystemExists(id: string): Promise<{ agent: Agent; agentSystem: AgentSystem }> {
    // Kiểm tra agent có tồn tại không
    const agent = await this.agentRepository.findOne({
      where: { id, deletedAt: IsNull() }
    });

    if (!agent) {
      throw new AppException(AGENT_ERROR_CODES.AGENT_NOT_FOUND);
    }

    // Kiểm tra agent system có tồn tại không
    const agentSystem = await this.agentSystemRepository.findOne({
      where: { id, deletedBy: IsNull() }
    });

    if (!agentSystem) {
      throw new AppException(AGENT_ERROR_CODES.AGENT_SYSTEM_NOT_FOUND);
    }

    return { agent, agentSystem };
  }

  /**
   * Validate unique nameCode for update (exclude current agent system)
   * @param nameCode Name code để kiểm tra
   * @param currentId ID của agent system hiện tại (để loại trừ)
   */
  private async validateUniqueNameCodeForUpdate(nameCode: string, currentId: string): Promise<void> {
    const existingAgentSystem = await this.agentSystemRepository.findOne({
      where: { nameCode, deletedBy: IsNull() }
    });

    if (existingAgentSystem && existingAgentSystem.id !== currentId) {
      throw new AppException(AGENT_ERROR_CODES.AGENT_SYSTEM_NAME_CODE_EXISTS);
    }
  }

  /**
   * Validate unique supervisor for update (exclude current agent system)
   * @param isSupervisor Có phải supervisor không
   * @param currentId ID của agent system hiện tại (để loại trừ)
   */
  private async validateUniqueSupervisorForUpdate(isSupervisor: boolean, currentId: string): Promise<void> {
    if (!isSupervisor) return; // Không cần validate nếu không phải supervisor

    const existingSupervisor = await this.agentSystemRepository.findOne({
      where: { isSupervisor: true, deletedBy: IsNull() }
    });

    if (existingSupervisor && existingSupervisor.id !== currentId) {
      throw new AppException(
        AGENT_ERROR_CODES.AGENT_SYSTEM_NAME_EXISTS, // Sử dụng error code có sẵn
        'Chỉ được có một agent system supervisor'
      );
    }
  }

  /**
   * Validate vector store exists (simplified validation)
   * @param vectorStoreId ID của vector store
   */
  private async validateVectorStore(vectorStoreId: string): Promise<void> {
    // TODO: Implement proper vector store validation when service is available
    // For now, just validate the format
    if (!vectorStoreId || vectorStoreId.trim().length === 0) {
      throw new AppException(
        AGENT_ERROR_CODES.VECTOR_STORE_NOT_FOUND,
        'Vector store ID không hợp lệ'
      );
    }

    this.logger.debug(`Vector store validation for ID: ${vectorStoreId} - basic format check passed`);
  }

  /**
   * Lấy thông tin system model - Optimized với repository method
   * @param systemModelId ID của system model
   * @returns SystemModelDto
   */
  private async getSystemModelInfo(systemModelId: string): Promise<SystemModelDto> {
    // Sử dụng repository method thay vì direct query
    const systemModel = await this.systemModelsRepository.findByIdWithRegistry(systemModelId);

    if (!systemModel) {
      throw new AppException(AGENT_ERROR_CODES.MODEL_NOT_FOUND);
    }

    // Business logic: Map từ SystemModelWithRegistry sang SystemModelDto
    return {
      id: systemModel.id,
      modelId: systemModel.modelId,
      provider: systemModel.provider,
      modelNamePattern: systemModel.modelNamePattern,
      active: systemModel.active,
      inputModalities: systemModel.inputModalities,
      outputModalities: systemModel.outputModalities,
      samplingParameters: systemModel.samplingParameters,
      features: systemModel.features,
      basePricing: systemModel.basePricing,
      fineTunePricing: systemModel.fineTunePricing,
      trainingPricing: systemModel.trainingPricing
    };
  }

  /**
   * Lấy danh sách MCP systems của agent - Optimized với repository method
   * @param agentId ID của agent
   * @returns Danh sách McpSystemDto
   */
  private async getMcpSystemsInfo(agentId: string): Promise<McpSystemDto[]> {
    // Sử dụng repository method thay vì direct query
    const mcpSystems = await this.agentSystemMcpRepository.findMcpSystemsWithDetailsByAgentId(agentId);

    // Business logic: Map sang McpSystemDto (nếu cần thêm logic)
    return mcpSystems.map(mcp => ({
      id: mcp.id,
      nameServer: mcp.nameServer,
      description: mcp.description,
      config: mcp.config,
      active: mcp.active,
      createdAt: mcp.createdAt,
      updatedAt: mcp.updatedAt
    }));
  }

  /**
   * Lấy thông tin vector store (simplified)
   * @param vectorStoreId ID của vector store
   * @returns Vector store info hoặc null
   */
  private async getVectorStoreInfo(vectorStoreId?: string | null): Promise<{ vectorStoreId: string; vectorStoreName: string } | null> {
    if (!vectorStoreId) {
      return null;
    }

    // TODO: Implement proper vector store service when available
    // For now, return basic info
    return {
      vectorStoreId,
      vectorStoreName: `Vector Store ${vectorStoreId.substring(0, 8)}`
    };
  }

  /**
   * Lấy thông tin employees (created, updated)
   * @param agentSystem Agent system entity
   * @returns Employee info
   */
  private async getEmployeeInfo(agentSystem: any): Promise<{
    created: { id: number; fullName: string; avatar?: string } | null;
    updated: { id: number; fullName: string; avatar?: string } | null;
  }> {
    const result = {
      created: null as { id: number; fullName: string; avatar?: string } | null,
      updated: null as { id: number; fullName: string; avatar?: string } | null
    };

    // Lấy thông tin employee created
    if (agentSystem.createdBy) {
      try {
        const createdEmployee = await this.employeeInfoService.getEmployeeInfo(agentSystem.createdBy);
        if (createdEmployee) {
          result.created = {
            id: createdEmployee.id,
            fullName: createdEmployee.name, // EmployeeInfoSimpleDto có trường 'name'
            avatar: createdEmployee.avatar || undefined
          };
        }
      } catch (error) {
        this.logger.warn(`Failed to get created employee info: ${error.message}`);
      }
    }

    // Lấy thông tin employee updated
    if (agentSystem.updatedBy && agentSystem.updatedBy !== agentSystem.createdBy) {
      try {
        const updatedEmployee = await this.employeeInfoService.getEmployeeInfo(agentSystem.updatedBy);
        if (updatedEmployee) {
          result.updated = {
            id: updatedEmployee.id,
            fullName: updatedEmployee.name, // EmployeeInfoSimpleDto có trường 'name'
            avatar: updatedEmployee.avatar || undefined
          };
        }
      } catch (error) {
        this.logger.warn(`Failed to get updated employee info: ${error.message}`);
      }
    } else if (agentSystem.updatedBy === agentSystem.createdBy) {
      // Nếu cùng một người thì copy thông tin
      result.updated = result.created;
    }

    return result;
  }

  /**
   * Map entities sang AgentSystemDetailDto
   * @param agent Agent entity
   * @param agentSystem AgentSystem entity
   * @param systemModel SystemModelDto
   * @param mcpSystems McpSystemDto[]
   * @param vectorStore Vector store info
   * @param employeeInfo Employee info
   * @returns AgentSystemDetailDto
   */
  private async mapToDetailDto(
    agent: any,
    agentSystem: any,
    systemModel: SystemModelDto,
    mcpSystems: McpSystemDto[],
    vectorStore: { vectorStoreId: string; vectorStoreName: string } | null,
    employeeInfo: {
      created: { id: number; fullName: string; avatar?: string } | null;
      updated: { id: number; fullName: string; avatar?: string } | null;
    }
  ): Promise<AgentSystemDetailDto> {
    // Generate avatar URL nếu có
    const avatarUrl = agent.avatar
      ? this.cdnService.generateUrlView(agent.avatar, TimeIntervalEnum.ONE_DAY)
      : null;

    // Map vector store
    const vectorDto = vectorStore ? {
      vectorStoreId: vectorStore.vectorStoreId,
      vectorStoreName: vectorStore.vectorStoreName
    } : undefined;

    // Map employee info
    const createdDto = employeeInfo.created ? {
      employeeId: employeeInfo.created.id,
      name: employeeInfo.created.fullName,
      avatar: employeeInfo.created.avatar || null
    } : undefined;

    const updatedDto = employeeInfo.updated ? {
      employeeId: employeeInfo.updated.id,
      name: employeeInfo.updated.fullName,
      avatar: employeeInfo.updated.avatar || null
    } : undefined;

    return {
      id: agentSystem.id,
      name: agent.name,
      nameCode: agentSystem.nameCode,
      avatar: avatarUrl,
      modelConfig: agent.modelConfig,
      instruction: agent.instruction,
      description: agentSystem.description,
      vector: vectorDto,
      model: systemModel,
      mcp: mcpSystems.length > 0 ? mcpSystems : undefined,
      created: createdDto,
      updated: updatedDto
    };
  }

  /**
   * Tạo agent record
   * @param createDto Dữ liệu tạo
   * @returns Agent ID
   */
  private async createAgentRecord(createDto: CreateAgentSystemDto): Promise<string> {
    const agentData = {
      name: createDto.name,
      modelConfig: createDto.modelConfig || {},
      instruction: createDto.instruction || '',
      avatar: null, // Sẽ được cập nhật sau khi upload
    };

    const savedAgent = await this.agentRepository.save(agentData);
    return savedAgent.id;
  }

  /**
   * Tạo agent system record
   * @param agentId Agent ID
   * @param createDto Dữ liệu tạo
   * @param employeeId Employee ID
   */
  private async createAgentSystemRecord(
    agentId: string,
    createDto: CreateAgentSystemDto,
    employeeId: number,
  ): Promise<void> {
    const agentSystemData = {
      id: agentId,
      nameCode: createDto.nameCode,
      description: createDto.description || undefined,
      isSupervisor: createDto.isSupervisor || false,
      active: true, // Default active
      systemModelId: createDto.modelId,
      createdBy: employeeId,
      updatedBy: employeeId,
    };

    await this.agentSystemRepository.save(agentSystemData);
  }

  /**
   * Liên kết agent với MCP systems
   * @param agentId Agent ID
   * @param mcpIds Danh sách MCP system IDs
   */
  private async linkAgentWithMcpSystems(agentId: string, mcpIds: string[]): Promise<void> {
    await this.agentSystemMcpRepository.linkAgentWithMcps(agentId, mcpIds);
  }
}
