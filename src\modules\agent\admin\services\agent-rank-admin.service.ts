import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Transactional } from 'typeorm-transactional';
import { AgentRank } from '@modules/agent/entities';
import { AgentRankRepository } from '@modules/agent/repositories';
import { AppException } from '@common/exceptions';
import { AGENT_RANK_ERROR_CODES } from '@modules/agent/exceptions';
import { PaginatedResult } from '@common/response';
import { CdnService } from '@shared/services/cdn.service';
import { S3Service } from '@shared/services/s3.service';
import { FileSizeEnum, TimeIntervalEnum } from '@shared/utils';
import { ImageTypeEnum } from '@shared/utils/file/image-media_type.util';
import { CategoryFolderEnum, generateS3Key } from '@shared/utils/generators/s3-key-generator.util';
import {
  AgentRankDetailDto,
  AgentRankListItemDto,
  AgentRankQueryDto,
  CreateAgentRankDto,
  CreateAgentRankResponseDto,
  UpdateAgentRankDto,
} from '../dto/agent-rank';

/**
 * Service xử lý logic nghiệp vụ liên quan đến cấp bậc agent cho admin
 */
@Injectable()
export class AgentRankAdminService {
  private readonly logger = new Logger(AgentRankAdminService.name);

  constructor(
    @InjectRepository(AgentRank)
    private readonly agentRankRepo: Repository<AgentRank>,
    private readonly agentRankRepository: AgentRankRepository,
    private readonly cdnService: CdnService,
    private readonly s3Service: S3Service,
  ) {}

  /**
   * Tạo mới cấp bậc agent
   * @param createDto Thông tin cấp bậc cần tạo
   * @param employeeId ID của nhân viên tạo
   * @returns URL để upload ảnh badge
   */
  @Transactional()
  async createRank(createDto: CreateAgentRankDto, employeeId: number): Promise<CreateAgentRankResponseDto> {
    try {
      // Kiểm tra tên đã tồn tại chưa
      const existingRank = await this.agentRankRepository.findByName(createDto.name);
      if (existingRank) {
        throw new AppException(AGENT_RANK_ERROR_CODES.AGENT_RANK_NAME_EXISTS);
      }

      // Kiểm tra khoảng exp có hợp lệ không
      if (createDto.minExp >= createDto.maxExp) {
        throw new AppException(AGENT_RANK_ERROR_CODES.AGENT_RANK_INVALID_EXP_RANGE);
      }

      // Kiểm tra chồng chéo khoảng exp
      const hasOverlap = await this.agentRankRepository.checkOverlap(createDto.minExp, createDto.maxExp);
      if (hasOverlap) {
        throw new AppException(AGENT_RANK_ERROR_CODES.AGENT_RANK_EXP_RANGE_OVERLAP);
      }

      // Tạo key cho badge trên S3
      const badgeKey = this.generateBadgeKey(employeeId, createDto.fileName);

      // Tạo mới cấp bậc
      const agentRank = this.agentRankRepo.create({
        name: createDto.name,
        description: createDto.description,
        badge: badgeKey, // Lưu key của badge thay vì URL
        minExp: createDto.minExp,
        maxExp: createDto.maxExp,
        active: createDto.active !== undefined ? createDto.active : false,
      });

      // Lưu vào database
      await this.agentRankRepo.save(agentRank);

      // Tạo URL để upload ảnh badge
      const uploadUrl = await this.s3Service.createPresignedWithID(
        badgeKey,
        TimeIntervalEnum.ONE_HOUR,
        ImageTypeEnum.PNG, // Content type for PNG images
        FileSizeEnum.FIVE_MB, // 5MB
      );

      // Trả về chỉ URL upload
      return {
        uploadUrl,
      };
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Lỗi khi tạo cấp bậc agent: ${error.message}`);
      throw new AppException(AGENT_RANK_ERROR_CODES.AGENT_RANK_CREATE_FAILED, error.message);
    }
  }

  /**
   * Tạo key cho badge trên S3 sử dụng utility function
   * @returns Key cho badge
   */
  private generateBadgeKey(employeeId: number, fileName: string): string {
    return generateS3Key({
      baseFolder: employeeId.toString(),
      categoryFolder: CategoryFolderEnum.IMAGE,
      fileName: fileName,
    });
  }

  /**
   * Cập nhật cấp bậc agent
   * @param id ID của cấp bậc cần cập nhật
   * @param updateDto Thông tin cần cập nhật
   * @returns Thông tin cấp bậc sau khi cập nhật
   */
  @Transactional()
  async updateRank(id: number, updateDto: UpdateAgentRankDto): Promise<AgentRankDetailDto> {
    try {
      // Kiểm tra cấp bậc tồn tại
      const agentRank = await this.agentRankRepository.findById(id);
      if (!agentRank) {
        throw new AppException(AGENT_RANK_ERROR_CODES.AGENT_RANK_NOT_FOUND);
      }

      // Kiểm tra tên đã tồn tại chưa (nếu có cập nhật tên)
      if (updateDto.name && updateDto.name !== agentRank.name) {
        const existingRank = await this.agentRankRepository.findByName(updateDto.name);
        if (existingRank && existingRank.id !== id) {
          throw new AppException(AGENT_RANK_ERROR_CODES.AGENT_RANK_NAME_EXISTS);
        }
      }

      // Xác định giá trị minExp và maxExp sau khi cập nhật
      const newMinExp = updateDto.minExp !== undefined ? updateDto.minExp : agentRank.minExp;
      const newMaxExp = updateDto.maxExp !== undefined ? updateDto.maxExp : agentRank.maxExp;

      // Kiểm tra khoảng exp có hợp lệ không
      if (newMinExp >= newMaxExp) {
        throw new AppException(AGENT_RANK_ERROR_CODES.AGENT_RANK_INVALID_EXP_RANGE);
      }

      // Kiểm tra chồng chéo khoảng exp (loại trừ cấp bậc hiện tại)
      const hasOverlap = await this.agentRankRepository.checkOverlap(newMinExp, newMaxExp, id);
      if (hasOverlap) {
        throw new AppException(AGENT_RANK_ERROR_CODES.AGENT_RANK_EXP_RANGE_OVERLAP);
      }

      // Cập nhật thông tin
      if (updateDto.name) agentRank.name = updateDto.name;
      if (updateDto.description !== undefined) agentRank.description = updateDto.description;
      if (updateDto.badge) agentRank.badge = updateDto.badge;
      if (updateDto.minExp !== undefined) agentRank.minExp = updateDto.minExp;
      if (updateDto.maxExp !== undefined) agentRank.maxExp = updateDto.maxExp;
      if (updateDto.active !== undefined) agentRank.active = updateDto.active;

      // Lưu vào database
      const updatedRank = await this.agentRankRepo.save(agentRank);

      // Chuyển đổi kết quả sang DTO
      return this.mapToAgentRankDetailDto(updatedRank);
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Lỗi khi cập nhật cấp bậc agent: ${error.message}`);
      throw new AppException(AGENT_RANK_ERROR_CODES.AGENT_RANK_UPDATE_FAILED, error.message);
    }
  }

  /**
   * Xóa cấp bậc agent
   * @param id ID của cấp bậc cần xóa
   */
  @Transactional()
  async deleteRank(id: number): Promise<void> {
    try {
      // Kiểm tra cấp bậc tồn tại
      const agentRank = await this.agentRankRepository.findById(id);
      if (!agentRank) {
        throw new AppException(AGENT_RANK_ERROR_CODES.AGENT_RANK_NOT_FOUND);
      }

      // Xóa cấp bậc
      await this.agentRankRepo.remove(agentRank);
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Lỗi khi xóa cấp bậc agent: ${error.message}`);
      throw new AppException(AGENT_RANK_ERROR_CODES.AGENT_RANK_DELETE_FAILED, error.message);
    }
  }

  /**
   * Lấy danh sách cấp bậc agent có phân trang
   * @param queryDto Tham số truy vấn
   * @returns Danh sách cấp bậc có phân trang
   */
  async getRanks(queryDto: AgentRankQueryDto): Promise<PaginatedResult<AgentRankListItemDto>> {
    try {
      // Lấy danh sách cấp bậc từ repository
      const result = await this.agentRankRepository.findPaginated(
        queryDto.page,
        queryDto.limit,
        queryDto.search,
        queryDto.sortBy,
        queryDto.sortDirection,
        queryDto.active,
      );

      // Chuyển đổi kết quả sang DTO
      const items = result.items.map(rank => this.mapToAgentRankListItemDto(rank));

      return {
        items,
        meta: result.meta,
      };
    } catch (error) {
      this.logger.error(`Lỗi khi lấy danh sách cấp bậc agent: ${error.message}`);
      throw new AppException(AGENT_RANK_ERROR_CODES.AGENT_RANK_FETCH_FAILED, error.message);
    }
  }





  /**
   * Chuyển đổi từ entity sang DTO
   * @param agentRank Entity AgentRank
   * @returns AgentRankListItemDto
   */
  private mapToAgentRankListItemDto(agentRank: AgentRank): AgentRankListItemDto {
    // Tạo URL CDN cho badge nếu có
    let badgeUrl = agentRank.badge;
    if (agentRank.badge) {
      try {
        // Sử dụng CdnService để tạo URL có chữ ký
        const normalizedKey = agentRank.badge.startsWith('/') ? agentRank.badge.substring(1) : agentRank.badge;
        const signedUrl = this.cdnService.generateUrlView(normalizedKey, TimeIntervalEnum.ONE_DAY);
        if (signedUrl) {
          badgeUrl = signedUrl;
        }
      } catch (error) {
        this.logger.warn(`Không thể tạo URL CDN cho badge: ${error.message}`);
      }
    }

    return {
      id: agentRank.id,
      name: agentRank.name,
      description: agentRank.description,
      badge: badgeUrl,
      minExp: agentRank.minExp,
      maxExp: agentRank.maxExp,
      active: agentRank.active,
    };
  }

  /**
   * Chuyển đổi từ entity sang DTO chi tiết
   * @param agentRank Entity AgentRank
   * @returns AgentRankDetailDto
   */
  private mapToAgentRankDetailDto(agentRank: AgentRank): AgentRankDetailDto {
    return this.mapToAgentRankListItemDto(agentRank);
  }
}
