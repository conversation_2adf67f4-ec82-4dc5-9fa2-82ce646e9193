# Agent System API Test Examples

## GET /admin/agent-systems/:id - <PERSON><PERSON><PERSON> chi tiết agent system

### Request
```http
GET /admin/agent-systems/550e8400-e29b-41d4-a716-446655440000
Authorization: Bearer <JWT_TOKEN>
```

### Response Success (200)
```json
{
  "success": true,
  "message": "Success",
  "data": {
    "id": "550e8400-e29b-41d4-a716-446655440000",
    "name": "System Assistant",
    "nameCode": "system_assistant",
    "avatar": "https://cdn.example.com/agent-avatars/system-assistant.png",
    "modelConfig": {
      "temperature": 0.7,
      "top_p": 0.9,
      "top_k": 40,
      "max_tokens": 1000
    },
    "instruction": "Bạn là trợ lý hệ thống, h<PERSON>y gi<PERSON>p người dùng giải đáp các thắc mắc",
    "description": "<PERSON><PERSON> tả về agent system supervisor",
    "vector": {
      "vectorStoreId": "vector-store-1",
      "vectorStoreName": "Vector Store 1"
    },
    "model": {
      "id": "123e4567-e89b-12d3-a456-426614174000",
      "modelId": "gpt-4o",
      "provider": "OPENAI",
      "modelNamePattern": "gpt-4*",
      "active": true,
      "inputModalities": ["TEXT", "IMAGE"],
      "outputModalities": ["TEXT"],
      "samplingParameters": ["TEMPERATURE", "TOP_P", "TOP_K"],
      "features": ["TOOL_CALL", "STRUCTURED_OUTPUT"],
      "basePricing": {
        "inputRate": 0.01,
        "outputRate": 0.03
      },
      "fineTunePricing": {
        "inputRate": 0.02,
        "outputRate": 0.06
      },
      "trainingPricing": 100
    },
    "mcp": [
      {
        "id": "456e7890-e89b-12d3-a456-426614174001",
        "nameServer": "filesystem-server",
        "description": "MCP server để quản lý hệ thống file",
        "config": {
          "command": "npx",
          "args": ["@modelcontextprotocol/server-filesystem", "/path/to/allowed/files"],
          "env": {
            "NODE_ENV": "production"
          }
        },
        "active": true,
        "createdAt": 1640995200000,
        "updatedAt": 1640995200000
      }
    ],
    "created": {
      "employeeId": 1,
      "name": "Nguyễn Văn A",
      "avatar": "https://cdn.example.com/employee-avatars/avatar1.png"
    },
    "updated": {
      "employeeId": 2,
      "name": "Trần Thị B",
      "avatar": "https://cdn.example.com/employee-avatars/avatar2.png"
    }
  }
}
```

### Response Error (404)
```json
{
  "success": false,
  "message": "Agent system không tồn tại",
  "errorCode": "AGENT_SYSTEM_NOT_FOUND",
  "data": null
}
```

## PATCH /admin/agent-systems/:id - Cập nhật agent system

### Request
```http
PATCH /admin/agent-systems/550e8400-e29b-41d4-a716-446655440000
Authorization: Bearer <JWT_TOKEN>
Content-Type: application/json

{
  "name": "Updated System Assistant",
  "nameCode": "updated_system_assistant",
  "description": "Mô tả đã được cập nhật",
  "modelId": "new-model-id-123",
  "instruction": "Hướng dẫn mới cho agent",
  "modelConfig": {
    "temperature": 0.8,
    "top_p": 0.95,
    "max_tokens": 2000
  },
  "vectorStoreId": "new-vector-store-id",
  "mcpId": ["mcp-1", "mcp-2"],
  "avatarMimeType": "image/png"
}
```

### Response Success (200)
```json
{
  "success": true,
  "message": "Cập nhật agent system thành công",
  "data": {
    "id": "550e8400-e29b-41d4-a716-446655440000",
    "avatarUrlUpload": "https://s3.amazonaws.com/bucket/presigned-upload-url?signature=..."
  }
}
```

### Response Error (400)
```json
{
  "success": false,
  "message": "Mã định danh đã tồn tại",
  "errorCode": "AGENT_SYSTEM_NAME_CODE_EXISTS",
  "data": null
}
```

## Test Cases

### 1. Test lấy chi tiết agent system thành công
- **Input**: Valid agent system ID
- **Expected**: Trả về đầy đủ thông tin agent system với SystemModelDto và McpSystemDto[]
- **Validation**: Kiểm tra tất cả trường có giá trị đúng

### 2. Test lấy chi tiết agent system không tồn tại
- **Input**: Invalid agent system ID
- **Expected**: Trả về lỗi AGENT_SYSTEM_NOT_FOUND
- **Validation**: Status code 404

### 3. Test cập nhật agent system thành công
- **Input**: Valid update data
- **Expected**: Trả về ID và avatarUrlUpload (nếu có)
- **Validation**: Dữ liệu được cập nhật trong database

### 4. Test cập nhật với nameCode trùng
- **Input**: nameCode đã tồn tại
- **Expected**: Trả về lỗi AGENT_SYSTEM_NAME_CODE_EXISTS
- **Validation**: Status code 400

### 5. Test cập nhật với model không tồn tại
- **Input**: modelId không hợp lệ
- **Expected**: Trả về lỗi MODEL_NOT_FOUND
- **Validation**: Status code 400

### 6. Test cập nhật với MCP systems không tồn tại
- **Input**: mcpId array chứa ID không hợp lệ
- **Expected**: Trả về lỗi tương ứng
- **Validation**: Status code 400

### 7. Test avatar upload URL generation
- **Input**: avatarMimeType trong update request
- **Expected**: Trả về avatarUrlUpload trong response
- **Validation**: URL có thể sử dụng để upload file
