import {ApiProperty, ApiPropertyOptional} from '@nestjs/swagger';
import {
  IsArray,
  IsBoolean,
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
  MaxLength,
  ValidateNested,
} from 'class-validator';
import {Type} from 'class-transformer';
import {TypeAgentStatus} from '@modules/agent/constants';
import {TypeAgentConfig} from '@modules/agent/interfaces/type-agent-config.interface';

/**
 * DTO cho cấu hình loại agent
 */
export class TypeAgentConfigDto implements TypeAgentConfig {
  /**
   * <PERSON><PERSON> hồ sơ không
   */
  @ApiProperty({
    description: '<PERSON><PERSON> hồ sơ không',
    example: true,
  })
  @IsBoolean()
  enableAgentProfileCustomization: boolean;

  /**
   * Có đầu ra không
   */
  @ApiProperty({
    description: 'Có đầu ra qua Messenger không',
    example: true,
  })
  @IsBoolean()
  enableOutputToMessenger: boolean;

  @ApiProperty({
    description: '<PERSON><PERSON> đầu ra qua Live Chat trên website không',
    example: true,
  })
  @IsBoolean()
  enableOutputToWebsiteLiveChat: boolean;

  /**
   * Có chuyển đổi không
   */
  @ApiProperty({
    description: 'Có chuyển đổi không',
    example: false,
  })
  @IsBoolean()
  enableTaskConversionTracking: boolean;

  /**
   * Có tài nguyên không
   */
  @ApiProperty({
    description: 'Có tài nguyên không',
    example: true,
  })
  @IsBoolean()
  enableResourceUsage: boolean;

  /**
   * Có chiến lược không
   */
  @ApiProperty({
    description: 'Có chiến lược không',
    example: true,
  })
  @IsBoolean()
  enableDynamicStrategyExecution: boolean;

  /**
   * Có đa agent không
   */
  @ApiProperty({
    description: 'Có đa agent không',
    example: true,
  })
  @IsBoolean()
  enableMultiAgentCollaboration: boolean;
}

/**
 * DTO cho việc tạo loại agent mới
 */
export class CreateTypeAgentDto {
  /**
   * Tên loại agent
   */
  @ApiProperty({
    description: 'Tên loại agent',
    example: 'Chatbot Agent',
  })
  @IsString()
  @IsNotEmpty()
  @MaxLength(255)
  name: string;

  /**
   * Mô tả chi tiết về loại agent
   */
  @ApiPropertyOptional({
    description: 'Mô tả chi tiết về loại agent',
    example: 'Loại agent hỗ trợ chat với người dùng',
  })
  @IsString()
  @IsOptional()
  description: string | null;

  /**
   * Cấu hình mặc định cho loại agent
   */
  @ApiProperty({
    description: 'Cấu hình mặc định cho loại agent',
    example: {
      : true,
      hasOutput: true,
      hasConversion: false,
      hasResources: true,
      hasStrategy: true,
      hasMultiAgent: false,
    },
  })
  @ValidateNested()
  @Type(() => TypeAgentConfigDto)
  defaultConfig: TypeAgentConfigDto;

  /**
   * Trạng thái của loại agent
   */
  @ApiPropertyOptional({
    description: 'Trạng thái của loại agent',
    enum: TypeAgentStatus,
    default: TypeAgentStatus.DRAFT,
  })
  @IsEnum(TypeAgentStatus)
  status: TypeAgentStatus;

  /**
   * Danh sách ID của các admin tools
   */
  @ApiProperty({
    description: 'Danh sách ID của các admin tools',
    example: ['uuid-1', 'uuid-2', 'uuid-3'],
    type: [String],
  })
  @IsArray()
  @IsString({ each: true })
  toolIds: string[];
}
