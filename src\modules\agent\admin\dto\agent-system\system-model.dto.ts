import { ProviderEnum } from '@modules/models/constants';
import {
  FeatureEnum,
  InputModalityEnum,
  OutputModalityEnum,
  SamplingParameterEnum
} from '@modules/models/constants/model-capabilities.enum';
import { ModelPricingInterface } from '@modules/models/interfaces/pricing.interface';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

/**
 * DTO cho thông tin system model trong agent system response
 */
export class SystemModelDto {
  /**
   * UUID của system model
   */
  @ApiProperty({
    description: 'UUID của system model',
    example: '123e4567-e89b-12d3-a456-************',
  })
  id: string;

  /**
   * ID định danh của model
   */
  @ApiProperty({
    description: 'ID định danh của model',
    example: 'gpt-4o',
  })
  modelId: string;

  /**
   * Nhà cung cấp model
   */
  @ApiProperty({
    description: 'Nhà cung cấp model',
    enum: ProviderEnum,
    example: ProviderEnum.OPENAI,
  })
  provider: ProviderEnum;

  /**
   * Tên mẫu đại diện của model
   */
  @ApiProperty({
    description: 'Tên mẫu đại diện của model',
    example: 'gpt-4*',
  })
  modelNamePattern: string;

  /**
   * Trạng thái hoạt động của model
   */
  @ApiProperty({
    description: 'Trạng thái hoạt động của model',
    example: true,
  })
  active: boolean;

  /**
   * Các phương thức input được hỗ trợ
   */
  @ApiPropertyOptional({
    description: 'Các phương thức input được hỗ trợ',
    enum: InputModalityEnum,
    isArray: true,
    example: [InputModalityEnum.TEXT, InputModalityEnum.IMAGE],
  })
  inputModalities?: InputModalityEnum[];

  /**
   * Các phương thức output được hỗ trợ
   */
  @ApiPropertyOptional({
    description: 'Các phương thức output được hỗ trợ',
    enum: OutputModalityEnum,
    isArray: true,
    example: [OutputModalityEnum.TEXT],
  })
  outputModalities?: OutputModalityEnum[];

  /**
   * Các tham số sampling được hỗ trợ
   */
  @ApiPropertyOptional({
    description: 'Các tham số sampling được hỗ trợ',
    enum: SamplingParameterEnum,
    isArray: true,
    example: [SamplingParameterEnum.TEMPERATURE, SamplingParameterEnum.TOP_P],
  })
  samplingParameters?: SamplingParameterEnum[];

  /**
   * Các tính năng được hỗ trợ
   */
  @ApiPropertyOptional({
    description: 'Các tính năng được hỗ trợ',
    enum: FeatureEnum,
    isArray: true,
    example: [FeatureEnum.TOOL_CALL, FeatureEnum.STRUCTURED_OUTPUT],
  })
  features?: FeatureEnum[];

  /**
   * Giá cơ bản cho model
   */
  @ApiPropertyOptional({
    description: 'Giá cơ bản cho model (input/output rate)',
    example: { inputRate: 0.01, outputRate: 0.03 },
  })
  basePricing?: ModelPricingInterface;

  /**
   * Giá fine-tune cho model
   */
  @ApiPropertyOptional({
    description: 'Giá fine-tune cho model (input/output rate)',
    example: { inputRate: 0.02, outputRate: 0.06 },
  })
  fineTunePricing?: ModelPricingInterface;

  /**
   * Giá training cho model
   */
  @ApiPropertyOptional({
    description: 'Giá training cho model',
    example: 100,
  })
  trainingPricing?: number;
}
