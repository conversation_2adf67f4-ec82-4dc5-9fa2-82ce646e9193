# Agent System DTO Examples

## SystemModelDto Example

```json
{
  "id": "123e4567-e89b-12d3-a456-426614174000",
  "modelId": "gpt-4o",
  "provider": "OPENAI",
  "modelNamePattern": "gpt-4*",
  "active": true,
  "inputModalities": ["TEXT", "IMAGE"],
  "outputModalities": ["TEXT"],
  "samplingParameters": ["TEMPERATURE", "TOP_P", "MAX_TOKENS"],
  "features": ["TOOL_CALL", "STREAMING"],
  "basePricing": {
    "inputRate": 0.01,
    "outputRate": 0.03
  },
  "fineTunePricing": {
    "inputRate": 0.02,
    "outputRate": 0.06
  },
  "trainingPricing": 100
}
```

## McpSystemDto Example

```json
{
  "id": "456e7890-e89b-12d3-a456-426614174001",
  "nameServer": "filesystem-server",
  "description": "MCP server để quản lý hệ thống file",
  "config": {
    "command": "npx",
    "args": ["@modelcontextprotocol/server-filesystem", "/path/to/allowed/files"],
    "env": {
      "NODE_ENV": "production"
    }
  },
  "active": true,
  "createdAt": 1640995200000,
  "updatedAt": 1640995200000
}
```

## AgentSystemDetailDto with new DTOs Example

```json
{
  "id": "550e8400-e29b-41d4-a716-446655440000",
  "name": "System Assistant",
  "nameCode": "system_assistant",
  "avatar": "https://example.com/system-assistant.png",
  "modelConfig": {
    "temperature": 0.7,
    "top_p": 0.9,
    "top_k": 40,
    "max_tokens": 1000
  },
  "instruction": "Bạn là trợ lý hệ thống, hãy giúp người dùng giải đáp các thắc mắc",
  "description": "Mô tả về agent system supervisor",
  "vector": {
    "vectorStoreId": "vector-store-1",
    "vectorStoreName": "Vector Store 1"
  },
  "model": {
    "id": "123e4567-e89b-12d3-a456-426614174000",
    "modelId": "gpt-4o",
    "provider": "OPENAI",
    "modelNamePattern": "gpt-4*",
    "active": true,
    "inputModalities": ["TEXT", "IMAGE"],
    "outputModalities": ["TEXT"],
    "samplingParameters": ["TEMPERATURE", "TOP_P", "MAX_TOKENS"],
    "features": ["TOOL_CALL", "STREAMING"],
    "basePricing": {
      "inputRate": 0.01,
      "outputRate": 0.03
    }
  },
  "mcp": [
    {
      "id": "456e7890-e89b-12d3-a456-426614174001",
      "nameServer": "filesystem-server",
      "description": "MCP server để quản lý hệ thống file",
      "config": {
        "command": "npx",
        "args": ["@modelcontextprotocol/server-filesystem", "/path/to/allowed/files"]
      },
      "active": true
    },
    {
      "id": "789e1234-e89b-12d3-a456-426614174002",
      "nameServer": "database-server",
      "description": "MCP server để truy vấn database",
      "config": {
        "command": "node",
        "args": ["./mcp-servers/database-server.js"]
      },
      "active": true
    }
  ],
  "created": {
    "id": 1,
    "fullName": "Nguyễn Văn A",
    "avatar": "https://example.com/avatar1.png"
  },
  "updated": {
    "id": 2,
    "fullName": "Trần Thị B",
    "avatar": "https://example.com/avatar2.png"
  }
}
```
