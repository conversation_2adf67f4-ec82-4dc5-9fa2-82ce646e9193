import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

/**
 * DTO cho thông tin MCP system trong agent system response
 */
export class McpSystemDto {
  /**
   * UUID của MCP system
   */
  @ApiProperty({
    description: 'UUID của MCP system',
    example: '123e4567-e89b-12d3-a456-************',
  })
  id: string;

  /**
   * Tên server MCP
   */
  @ApiProperty({
    description: 'Tên server MCP',
    example: 'filesystem-server',
  })
  nameServer: string;

  /**
   * Mô tả về MCP system
   */
  @ApiPropertyOptional({
    description: 'Mô tả về MCP system',
    example: 'MCP server để quản lý hệ thống file',
  })
  description?: string;

  /**
   * Cấu hình MCP system
   */
  @ApiProperty({
    description: 'Cấu hình MCP system',
    example: {
      command: 'npx',
      args: ['@modelcontextprotocol/server-filesystem', '/path/to/allowed/files'],
      env: {
        NODE_ENV: 'production'
      }
    },
  })
  config: Record<string, any>;
}
