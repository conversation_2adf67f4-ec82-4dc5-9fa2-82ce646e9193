import { Injectable, Logger } from '@nestjs/common';
import { DataSource, Repository, SelectQueryBuilder } from 'typeorm';
import { SystemModels } from '../entities/system-models.entity';
import { PaginatedResult } from '@common/response';
import { ProviderEnum } from '../constants';
import {
  InputModalityEnum,
  OutputModalityEnum,
  SamplingParameterEnum,
  FeatureEnum
} from '../constants/model-capabilities.enum';
import { ModelPricingInterface } from '../interfaces/pricing.interface';
import { SystemModelsQueryDto } from '../admin/dto/system-models';
import { SystemModelsQueryDto as UserSystemModelsQueryDto } from '../user/dto/user-models/system-models-query.dto';

/**
 * Interface cho system model kết hợp với thông tin model registry
 */
export interface SystemModelWithRegistry {
  id: string;
  modelId: string;
  provider: ProviderEnum;
  modelNamePattern: string;
  active: boolean;
  inputModalities: InputModalityEnum[];
  outputModalities: OutputModalityEnum[];
  samplingParameters: SamplingParameterEnum[];
  features: FeatureEnum[];
  basePricing: ModelPricingInterface;
  fineTunePricing: ModelPricingInterface;
  trainingPricing: number;
}

/**
 * Repository cho SystemModels
 * Xử lý các thao tác CRUD và truy vấn nâng cao liên quan đến system models
 */
@Injectable()
export class SystemModelsRepository extends Repository<SystemModels> {
  private readonly logger = new Logger(SystemModelsRepository.name);

  constructor(private dataSource: DataSource) {
    super(SystemModels, dataSource.createEntityManager());
  }

  /**
   * Mapper function để chuyển đổi raw result thành SystemModelWithRegistry
   * @param rawResult Raw result từ database query
   * @returns SystemModelWithRegistry object
   */
  private mapRawResultToSystemModel(rawResult: Record<string, any>): SystemModelWithRegistry {
    return {
      id: rawResult.systemModels_id,
      modelId: rawResult.systemModels_model_id,
      provider: rawResult.system_provider,
      modelNamePattern: rawResult.registry_model_name_pattern,
      active: rawResult.systemModels_active,
      inputModalities: rawResult.registry_input_modalities || [],
      outputModalities: rawResult.registry_output_modalities || [],
      samplingParameters: rawResult.registry_sampling_parameters || [],
      features: rawResult.registry_features || [],
      basePricing: rawResult.registry_base_pricing,
      fineTunePricing: rawResult.registry_fine_tune_pricing,
      trainingPricing: rawResult.registry_training_pricing
    };
  }

  /**
   * Tạo query builder cơ bản cho SystemModels với join model_registry
   * @returns SelectQueryBuilder cho SystemModels với thông tin model registry
   */
  createBaseQuery(): SelectQueryBuilder<SystemModels> {
    return this.createQueryBuilder('systemModels')
      .leftJoin('model_registry', 'registry', 'systemModels.modelRegistryId = registry.id')
      .select([
        '"systemModels"."id" AS "systemModels_id"',
        '"systemModels"."model_id" AS "systemModels_model_id"',
        '"systemModels"."active" AS "systemModels_active"',
        '"systemModels"."provider" AS "system_provider"',
        '"registry"."model_name_pattern" AS "registry_model_name_pattern"',
        '"registry"."input_modalities" AS "registry_input_modalities"',
        '"registry"."output_modalities" AS "registry_output_modalities"',
        '"registry"."sampling_parameters" AS "registry_sampling_parameters"',
        '"registry"."features" AS "registry_features"',
        '"registry"."base_pricing" AS "registry_base_pricing"',
        '"registry"."fine_tune_pricing" AS "registry_fine_tune_pricing"',
        '"registry"."training_pricing" AS "registry_training_pricing"'
      ]);
  }

  /**
   * Lấy danh sách system models theo provider với phân trang
   * @param queryDto Query parameters
   * @returns Kết quả phân trang với thông tin model registry
   */
  async findByProviderWithPagination(queryDto: SystemModelsQueryDto): Promise<PaginatedResult<SystemModelWithRegistry>> {
    const query = this.createBaseQuery();

    // Lọc theo provider (bắt buộc)
    query.andWhere('"systemModels"."provider" = :provider', { provider: queryDto.provider });

    // Lọc theo trạng thái active
    if (queryDto.active !== undefined) {
      query.andWhere('systemModels.active = :active', { active: queryDto.active });
    }

    // Tìm kiếm chung
    if (queryDto.search) {
      query.andWhere(
        '(systemModels.model_id ILIKE :search)',
        { search: `%${queryDto.search}%` }
      );
    }

    // Sắp xếp
    if (queryDto.sortBy) {
      const direction = queryDto.sortDirection || 'ASC';

      // Map sortBy values to proper alias names với quotes
      const sortFieldMap: Record<string, string> = {
        'systemModels.modelId': '"systemModels_model_id"',
        'systemModels.active': '"systemModels_active"',
        'modelId': '"systemModels_model_id"',
        'active': '"systemModels_active"'
      };

      const sortField = sortFieldMap[queryDto.sortBy] || '"systemModels_model_id"';

      query.orderBy(sortField as any, direction);
    } else {
      query.orderBy('"systemModels_model_id"', 'ASC');
    }

    // Phân trang
    const skip = (queryDto.page - 1) * queryDto.limit;
    query.skip(skip).take(queryDto.limit);

    // Sử dụng getRawMany để lấy dữ liệu từ join
    const totalItems = await query.getCount();
    const rawResults = await query.getRawMany();

    // Transform raw results using mapper
    const items: SystemModelWithRegistry[] = rawResults.map(result =>
      this.mapRawResultToSystemModel(result)
    );

    return {
      items,
      meta: {
        totalItems,
        itemCount: items.length,
        itemsPerPage: queryDto.limit,
        totalPages: Math.ceil(totalItems / queryDto.limit),
        currentPage: queryDto.page
      }
    };
  }

  /**
   * Lấy danh sách system models theo provider (không phân trang)
   * @param provider Nhà cung cấp
   * @returns Danh sách system models với thông tin model registry
   */
  async findByProvider(provider: ProviderEnum): Promise<SystemModelWithRegistry[]> {
    const query = this.createBaseQuery()
      .andWhere('"systemModels"."provider" = :provider', { provider })
      .orderBy('"systemModels_model_id"', 'ASC');

    const rawResults = await query.getRawMany();

    // Transform raw results using mapper
    return rawResults.map(result => this.mapRawResultToSystemModel(result));
  }

  /**
   * Tìm system model theo model ID
   * @param modelId ID của model
   * @returns System model hoặc null
   */
  async findByModelId(modelId: string): Promise<SystemModels | null> {
    return this.findOne({
      where: { modelId }
    });
  }

  /**
   * Kiểm tra system model có tồn tại không
   * @param id ID của system model
   * @returns True nếu tồn tại
   */
  async isExists(id: string): Promise<boolean> {
    const count = await this.createQueryBuilder('systemModels')
      .where('systemModels.id = :id', { id })
      .getCount();

    return count > 0;
  }

  /**
   * Lấy system model theo ID
   * @param id ID của system model
   * @returns System model hoặc null
   */
  async findById(id: string): Promise<SystemModels | null> {
    return this.createQueryBuilder('systemModels')
      .where('systemModels.id = :id', { id })
      .getOne();
  }

  /**
   * Bật/tắt trạng thái active của system model
   * @param id ID của system model
   * @returns System model đã cập nhật
   */
  async toggleActive(id: string): Promise<SystemModels | null> {
    // Lấy trạng thái hiện tại
    const systemModel = await this.findById(id);
    if (!systemModel) {
      return null;
    }

    // Đảo ngược trạng thái active
    const newActiveStatus = !systemModel.active;

    // Cập nhật trạng thái
    await this.createQueryBuilder()
      .update(SystemModels)
      .set({
        active: newActiveStatus,
      })
      .where('id = :id', { id })
      .execute();

    // Trả về system model đã cập nhật
    return this.findById(id);
  }

  /**
   * Lấy danh sách active system models với pagination cho user
   * @param queryDto Query parameters từ user
   * @returns Kết quả phân trang với thông tin model registry
   */
  async findActiveWithPagination(queryDto: UserSystemModelsQueryDto): Promise<PaginatedResult<SystemModelWithRegistry>> {
    const query = this.createBaseQuery();

    // Lọc theo active = true (mặc định)
    query.andWhere('systemModels.active = :active', { active: true });

    // Lọc theo provider nếu có
    if (queryDto.provider) {
      query.andWhere('"systemModels"."provider" = :provider', { provider: queryDto.provider });
    }

    // Tìm kiếm chung
    if (queryDto.search) {
      query.andWhere(
        '(systemModels.model_id ILIKE :search)',
        { search: `%${queryDto.search}%` }
      );
    }

    // Pagination
    const page = queryDto.page || 1;
    const limit = queryDto.limit || 10;
    const skip = (page - 1) * limit;

    query.skip(skip).take(limit);

    const [rawResults, total] = await Promise.all([
      query.getRawMany(),
      query.getCount()
    ]);

    // Transform raw results using mapper
    const items = rawResults.map(result => this.mapRawResultToSystemModel(result));

    return {
      items,
      meta: {
        totalItems: total,
        itemCount: items.length,
        itemsPerPage: limit,
        totalPages: Math.ceil(total / limit),
        currentPage: page,
        hasItems: total > 0,
      }
    };
  }

  /**
   * Lấy thông tin system model với model registry theo ID
   * @param systemModelId ID của system model
   * @returns SystemModelWithRegistry hoặc null nếu không tìm thấy
   */
  async findByIdWithRegistry(systemModelId: string): Promise<SystemModelWithRegistry | null> {
    const query = this.createQueryBuilder('sm')
      .leftJoin('model_registry', 'mr', 'sm.model_registry_id = mr.id AND sm.provider = mr.provider')
      .select([
        'sm.id as id',
        'sm.model_id as modelId',
        'sm.provider as provider',
        'sm.active as active',
        'mr.model_name_pattern as modelNamePattern',
        'mr.input_modalities as inputModalities',
        'mr.output_modalities as outputModalities',
        'mr.sampling_parameters as samplingParameters',
        'mr.features as features',
        'mr.base_pricing as basePricing',
        'mr.fine_tune_pricing as fineTunePricing',
        'mr.training_pricing as trainingPricing'
      ])
      .where('sm.id = :systemModelId', { systemModelId });

    const rawResult = await query.getRawOne();

    if (!rawResult) {
      return null;
    }

    return {
      id: rawResult.id,
      modelId: rawResult.modelId,
      provider: rawResult.provider,
      modelNamePattern: rawResult.modelNamePattern || rawResult.modelId,
      active: rawResult.active,
      inputModalities: rawResult.inputModalities,
      outputModalities: rawResult.outputModalities,
      samplingParameters: rawResult.samplingParameters,
      features: rawResult.features,
      basePricing: rawResult.basePricing,
      fineTunePricing: rawResult.fineTunePricing,
      trainingPricing: rawResult.trainingPricing
    };
  }
}
