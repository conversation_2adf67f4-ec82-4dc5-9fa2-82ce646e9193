import { Injectable, Logger } from '@nestjs/common';
import { TypeAgentRepository } from '@modules/agent/repositories/type-agent.repository';
import {
  CreateTypeAgentDto,
  TypeAgentDetailDto,
  TypeAgentListItemDto,
  TypeAgentQueryDto,
  TypeAgentTrashItemDto,
  UpdateTypeAgentDto,
  UpdateTypeAgentStatusDto,
} from '../dto/type-agent';
import { TypeAgent } from '@modules/agent/entities';
import { TypeAgentStatus } from '@modules/agent/constants';
import { PaginatedResult } from '@/common/response';
import { AppException } from '@common/exceptions';
import { AGENT_ERROR_CODES } from '@modules/agent/exceptions/agent-error.code';
import { Transactional } from 'typeorm-transactional';

import { EmployeeInfoService } from '@modules/employee/services/employee-info.service';
import { EmployeeInfoDto } from '@modules/agent/admin/dto/common';
import { EmployeeInfoSimpleDto } from '@/modules/employee/dto/employee-info-simple.dto';

/**
 * Service xử lý logic nghiệp vụ cho Type Agent
 */
@Injectable()
export class AdminTypeAgentService {
  private readonly logger = new Logger(AdminTypeAgentService.name);

  constructor(
    private readonly typeAgentRepository: TypeAgentRepository,
    private readonly employeeInfoService: EmployeeInfoService,
  ) {}

  /**
   * Lấy danh sách loại agent với phân trang
   * @param queryDto Tham số truy vấn
   * @returns Danh sách loại agent với phân trang
   */
  async findAll(
    queryDto: TypeAgentQueryDto,
  ): Promise<PaginatedResult<TypeAgentListItemDto>> {
    try {
      const { page, limit, search, sortBy, sortDirection } = queryDto;

      // Lấy danh sách loại agent từ repository (không filter theo status)
      const result = await this.typeAgentRepository.findPaginated(
        page,
        limit,
        search,
        undefined, // Không filter theo status
        undefined,
        sortBy,
        sortDirection,
      );

      // Chuyển đổi kết quả sang DTO
      const items = await Promise.all(
        result.items.map((typeAgent) => this.mapToListItemDto(typeAgent))
      );

      return {
        items,
        meta: result.meta,
      };
    } catch (error) {
      this.logger.error(
        `Error finding type agents: ${error.message}`,
        error.stack,
      );
      throw new AppException(AGENT_ERROR_CODES.TYPE_AGENT_QUERY_FAILED);
    }
  }

  /**
   * Lấy thông tin chi tiết loại agent theo ID
   * @param id ID của loại agent
   * @returns Thông tin chi tiết loại agent
   */
  async findById(id: number): Promise<TypeAgentDetailDto> {
    // Lấy thông tin loại agent từ repository
    const typeAgent = await this.typeAgentRepository.findById(id);

    if (!typeAgent) {
      throw new AppException(AGENT_ERROR_CODES.TYPE_AGENT_NOT_FOUND);
    }

    try {
      // Lấy danh sách agent systems
      const agentSystems = await this.typeAgentRepository.findAgentSystemsByTypeAgentId(id);

      // Lấy thông tin employee
      const employeeInfo = await this.getEmployeeInfoForTypeAgent(typeAgent);

      return await this.mapToDetailDto(typeAgent, agentSystems, employeeInfo);
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(
        `Error finding type agent by id: ${error.message}`,
        error.stack,
      );
      throw new AppException(AGENT_ERROR_CODES.TYPE_AGENT_FETCH_FAILED);
    }
  }

  /**
   * Tạo loại agent mới
   * @param createDto Dữ liệu tạo loại agent
   * @param employeeId ID của nhân viên tạo
   * @returns ID của loại agent đã tạo
   */
  @Transactional()
  async create(
    createDto: CreateTypeAgentDto,
    employeeId: number,
  ): Promise<number> {
    // Kiểm tra tên loại agent đã tồn tại chưa
    const existingTypeAgent = await this.typeAgentRepository.findByName(
      createDto.name,
    );
    if (existingTypeAgent) {
      throw new AppException(AGENT_ERROR_CODES.TYPE_AGENT_NAME_EXISTS);
    }

    try {
      // Tạo loại agent mới
      const typeAgent = new TypeAgent();
      typeAgent.name = createDto.name;
      typeAgent.description = createDto.description;
      typeAgent.config = createDto.defaultConfig;
      typeAgent.status = createDto.status;
      typeAgent.createdBy = employeeId;
      typeAgent.updatedBy = employeeId;

      // Lưu loại agent
      const savedTypeAgent = await this.typeAgentRepository.save(typeAgent);

      return savedTypeAgent.id;
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(
        `Error creating type agent: ${error.message}`,
        error.stack,
      );
      throw new AppException(AGENT_ERROR_CODES.TYPE_AGENT_CREATION_FAILED);
    }
  }

  /**
   * Cập nhật thông tin loại agent
   * @param id ID của loại agent
   * @param updateDto Dữ liệu cập nhật
   * @param employeeId ID của nhân viên cập nhật
   */
  @Transactional()
  async update(
    id: number,
    updateDto: UpdateTypeAgentDto,
    employeeId: number,
  ): Promise<void> {
    // Kiểm tra loại agent có tồn tại không
    const typeAgent = await this.typeAgentRepository.findById(id);
    if (!typeAgent) {
      throw new AppException(AGENT_ERROR_CODES.TYPE_AGENT_NOT_FOUND);
    }

    // Kiểm tra xem loại agent đã bị xóa mềm hay chưa
    if (typeAgent.deletedAt) {
      throw new AppException(AGENT_ERROR_CODES.TYPE_AGENT_ALREADY_DELETED);
    }

    // Kiểm tra tên loại agent đã tồn tại chưa (nếu có cập nhật tên)
    if (updateDto.name && updateDto.name !== typeAgent.name) {
      const existingTypeAgent = await this.typeAgentRepository.findByName(
        updateDto.name,
      );
      if (existingTypeAgent && existingTypeAgent.id !== id) {
        throw new AppException(AGENT_ERROR_CODES.TYPE_AGENT_NAME_EXISTS);
      }
    }
    try {
      // Cập nhật thông tin loại agent
      if (updateDto.name) typeAgent.name = updateDto.name;
      if (updateDto.description !== undefined)
        typeAgent.description = updateDto.description;
      if (updateDto.defaultConfig) typeAgent.config = updateDto.defaultConfig;
      if (updateDto.status) typeAgent.status = updateDto.status;
      typeAgent.updatedBy = employeeId;

      // Lưu loại agent
      await this.typeAgentRepository.save(typeAgent);

    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(
        `Error updating type agent: ${error.message}`,
        error.stack,
      );
      throw new AppException(AGENT_ERROR_CODES.TYPE_AGENT_UPDATE_FAILED);
    }
  }

  /**
   * Cập nhật trạng thái loại agent
   * @param id ID của loại agent
   * @param updateStatusDto Dữ liệu cập nhật trạng thái
   * @param employeeId ID của nhân viên cập nhật
   */
  @Transactional()
  async updateStatus(
    id: number,
    updateStatusDto: UpdateTypeAgentStatusDto,
    employeeId: number,
  ): Promise<void> {
    // Kiểm tra loại agent có tồn tại không
    const typeAgent = await this.typeAgentRepository.findById(id);
    if (!typeAgent) {
      throw new AppException(AGENT_ERROR_CODES.TYPE_AGENT_NOT_FOUND);
    }

    // Kiểm tra xem loại agent đã bị xóa mềm hay chưa
    if (typeAgent.deletedAt) {
      throw new AppException(AGENT_ERROR_CODES.TYPE_AGENT_ALREADY_DELETED);
    }
    try {
      // Cập nhật trạng thái
      typeAgent.status = updateStatusDto.status;
      typeAgent.updatedBy = employeeId;

      // Lưu loại agent
      await this.typeAgentRepository.save(typeAgent);
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(
        `Error updating type agent status: ${error.message}`,
        error.stack,
      );
      throw new AppException(AGENT_ERROR_CODES.TYPE_AGENT_STATUS_UPDATE_FAILED);
    }
  }

  /**
   * Xóa loại agent (soft delete)
   * @param id ID của loại agent
   * @param employeeId ID của nhân viên xóa
   */
  @Transactional()
  async remove(id: number, employeeId: number): Promise<void> {
    // Kiểm tra loại agent có tồn tại không
    const typeAgent = await this.typeAgentRepository.findById(id);
    if (!typeAgent) {
      throw new AppException(AGENT_ERROR_CODES.TYPE_AGENT_NOT_FOUND);
    }
    try {
      // Sử dụng phương thức xóa mềm tùy chỉnh thay vì softDelete của TypeORM
      const result = await this.typeAgentRepository.customSoftDelete(
        id,
        employeeId,
      );

      if (!result) {
        throw new AppException(AGENT_ERROR_CODES.TYPE_AGENT_DELETE_FAILED);
      }

    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(
        `Error removing type agent: ${error.message}`,
        error.stack,
      );
      throw new AppException(AGENT_ERROR_CODES.TYPE_AGENT_DELETE_FAILED);
    }
  }





  /**
   * Cập nhật trạng thái type agent theo query param
   * @param id ID của type agent
   * @param draft true để chuyển về DRAFT, false để chuyển về APPROVED
   * @param employeeId ID của nhân viên cập nhật
   */
  async updateStatusByQuery(id: number, draft: boolean, employeeId: number): Promise<void> {
    try {
      // Kiểm tra type agent có tồn tại không
      const typeAgent = await this.typeAgentRepository.findById(id);
      if (!typeAgent) {
        throw new AppException(AGENT_ERROR_CODES.TYPE_AGENT_NOT_FOUND);
      }

      // Xác định status mới
      const newStatus = draft ? TypeAgentStatus.DRAFT : TypeAgentStatus.APPROVED;

      // Cập nhật status
      typeAgent.status = newStatus;
      typeAgent.updatedBy = employeeId;

      await this.typeAgentRepository.save(typeAgent);
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Error updating type agent status: ${error.message}`, error.stack);
      throw new AppException(AGENT_ERROR_CODES.TYPE_AGENT_STATUS_UPDATE_FAILED);
    }
  }

  /**
   * Xóa type agent với migration agents sang type mới
   * @param id ID của type agent cần xóa
   * @param newTypeAgentId ID của type agent mới để migrate
   * @param employeeId ID của nhân viên xóa
   * @returns Số lượng agents đã được migrate
   */
  async removeWithMigration(id: number, newTypeAgentId: number, employeeId: number): Promise<number> {
    try {
      // Kiểm tra type agent cần xóa có tồn tại không
      const typeAgent = await this.typeAgentRepository.findById(id);
      if (!typeAgent) {
        throw new AppException(AGENT_ERROR_CODES.TYPE_AGENT_NOT_FOUND);
      }

      // Kiểm tra type agent mới có tồn tại không
      const newTypeAgent = await this.typeAgentRepository.findById(newTypeAgentId);
      if (!newTypeAgent) {
        throw new AppException(AGENT_ERROR_CODES.TYPE_AGENT_NOT_FOUND);
      }

      // Migrate agents sang type mới
      const migratedCount = await this.typeAgentRepository.migrateAgents(id, newTypeAgentId);

      // Soft delete type agent
      await this.typeAgentRepository.customSoftDelete(id, employeeId);

      return migratedCount;
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Error removing type agent with migration: ${error.message}`, error.stack);
      throw new AppException(AGENT_ERROR_CODES.TYPE_AGENT_DELETE_FAILED);
    }
  }

  /**
   * Lấy danh sách type agents đã xóa mềm
   * @param queryDto Tham số truy vấn
   * @returns Danh sách type agents đã xóa với phân trang
   */
  async findAllDeleted(queryDto: TypeAgentQueryDto): Promise<PaginatedResult<TypeAgentTrashItemDto>> {
    try {
      const result = await this.typeAgentRepository.findDeletedPaginated(
        queryDto.page,
        queryDto.limit,
        queryDto.search,
        queryDto.sortBy,
        queryDto.sortDirection,
      );

      // Chuyển đổi kết quả sang DTO
      const items = await Promise.all(
        result.items.map((typeAgent) => this.mapToTrashItemDto(typeAgent))
      );

      return {
        items,
        meta: result.meta,
      };
    } catch (error) {
      this.logger.error(`Error fetching deleted type agents: ${error.message}`, error.stack);
      throw new AppException(AGENT_ERROR_CODES.TYPE_AGENT_QUERY_FAILED);
    }
  }

  /**
   * Khôi phục type agent đã xóa mềm (single)
   * @param id ID của type agent cần khôi phục
   * @param employeeId ID của nhân viên khôi phục
   */
  async restore(id: number, employeeId: number): Promise<void> {
    return this.restoreBulk([id], employeeId);
  }

  /**
   * Khôi phục nhiều type agents đã xóa mềm (bulk)
   * @param ids Danh sách ID của các type agent cần khôi phục
   * @param employeeId ID của nhân viên khôi phục
   */
  async restoreBulk(ids: number[], employeeId: number): Promise<void> {
    try {
      this.logger.log(`Bulk restoring type agents: ${ids.join(', ')} by employee ${employeeId}`);

      if (!ids || ids.length === 0) {
        this.logger.warn('No IDs provided for bulk restore');
        return;
      }

      // Khôi phục từng type agent
      const results = await Promise.allSettled(
        ids.map(id => this.typeAgentRepository.restoreTypeAgent(id))
      );

      // Đếm số lượng thành công và thất bại
      const successCount = results.filter(result => result.status === 'fulfilled' && result.value).length;
      const failedCount = results.length - successCount;

      this.logger.log(`Bulk restore completed. Success: ${successCount}, Failed: ${failedCount}`);

      if (successCount === 0) {
        throw new AppException(AGENT_ERROR_CODES.TYPE_AGENT_NOT_FOUND, 'Không tìm thấy type agent nào để khôi phục');
      }

    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Error in bulk restore type agents: ${error.message}`, error.stack);
      throw new AppException(AGENT_ERROR_CODES.TYPE_AGENT_DELETE_FAILED);
    }
  }



  /**
   * Khôi phục nhiều type agents đã xóa mềm
   * @param ids Danh sách ID của các type agents cần khôi phục
   * @returns Số lượng type agents đã khôi phục thành công
   */
  async bulkRestore(ids: number[]): Promise<number> {
    try {
      let restoredCount = 0;

      // Xử lý từng type agent
      for (const id of ids) {
        try {
          const success = await this.typeAgentRepository.restoreTypeAgent(id);
          if (success) {
            restoredCount++;
          } else {
            this.logger.warn(`Type agent ${id} không thể khôi phục (có thể không tồn tại hoặc chưa bị xóa)`);
          }
        } catch (error) {
          this.logger.error(`Lỗi khi khôi phục type agent ${id}: ${error.message}`);
          // Tiếp tục với các ID khác
        }
      }

      return restoredCount;
    } catch (error) {
      this.logger.error(`Error bulk restoring type agents: ${error.message}`, error.stack);
      throw new AppException(AGENT_ERROR_CODES.TYPE_AGENT_DELETE_FAILED);
    }
  }

  /**
   * Chuyển đổi TypeAgent thành TypeAgentListItemDto
   * @param typeAgent Entity TypeAgent
   * @returns TypeAgentListItemDto
   */
  private async mapToListItemDto(
    typeAgent: TypeAgent,
  ): Promise<TypeAgentListItemDto> {

    return {
      id: typeAgent.id,
      name: typeAgent.name,
      description: typeAgent.description,
      createdAt: typeAgent.createdAt,
      status: typeAgent.status,
    };
  }

  /**
   * Chuyển đổi TypeAgent thành TypeAgentDetailDto
   * @param typeAgent Entity TypeAgent
   * @param agentSystems Danh sách agent systems
   * @param employeeInfo Thông tin employee
   * @returns TypeAgentDetailDto
   */
  private async mapToDetailDto(
    typeAgent: TypeAgent,
    agentSystems: { id: string; name: string; avatar: string | null; model: string }[],
    employeeInfo: {
      created: EmployeeInfoDto | undefined;
      updated: EmployeeInfoDto | undefined;
      deleted: EmployeeInfoDto | undefined;
    }
  ): Promise<TypeAgentDetailDto> {

    return {
      id: typeAgent.id,
      name: typeAgent.name,
      description: typeAgent.description,
      defaultConfig: typeAgent.config,
      status: typeAgent.status,
      createdAt: typeAgent.createdAt,
      updatedAt: typeAgent.updatedAt,
      agentSystems: agentSystems,
      created: employeeInfo.created,
      updated: employeeInfo.updated,
      deleted: employeeInfo.deleted,
    };
  }

  /**
   * Chuyển đổi TypeAgent thành TypeAgentTrashItemDto
   * @param typeAgent Entity TypeAgent đã bị xóa
   * @returns TypeAgentTrashItemDto
   */
  private async mapToTrashItemDto(typeAgent: TypeAgent): Promise<TypeAgentTrashItemDto> {
    // Lấy thông tin người xóa
    let deletedInfo: EmployeeInfoSimpleDto | null = null;
    if (typeAgent.deletedBy) {
      try {
        deletedInfo = await this.employeeInfoService.getEmployeeInfo(typeAgent.deletedBy);
      } catch (error) {
        this.logger.warn(`Cannot get deleted employee info for ID ${typeAgent.deletedBy}`);
        deletedInfo = {
          id: typeAgent.deletedBy,
          name: 'Unknown',
          avatar: null,
        };
      }
    }

    return {
      id: typeAgent.id,
      name: typeAgent.name,
      description: typeAgent.description,
      createdAt: typeAgent.createdAt,
      deletedAt: typeAgent.deletedAt || 0,
      deleted: deletedInfo || {
        id: 0,
        name: 'Unknown',
        avatar: null,
      },
    };
  }

  /**
   * Lấy thông tin employee cho type agent
   * @param typeAgent Entity TypeAgent
   * @returns Thông tin employee
   */
  private async getEmployeeInfoForTypeAgent(typeAgent: TypeAgent): Promise<{
    created: EmployeeInfoDto | undefined;
    updated: EmployeeInfoDto | undefined;
    deleted: EmployeeInfoDto | undefined;
  }> {
    const result = {
      created: undefined as EmployeeInfoDto | undefined,
      updated: undefined as EmployeeInfoDto | undefined,
      deleted: undefined as EmployeeInfoDto | undefined,
    };

    // Lấy thông tin người tạo
    if (typeAgent.createdBy) {
      try {
        const createdInfo = await this.employeeInfoService.getEmployeeInfo(typeAgent.createdBy);
        result.created = {
          employeeId: createdInfo.id,
          name: createdInfo.name,
          avatar: createdInfo.avatar || null,
        };
      } catch (error) {
        this.logger.warn(`Cannot get created employee info for ID ${typeAgent.createdBy}`);
      }
    }

    // Lấy thông tin người cập nhật
    if (typeAgent.updatedBy && typeAgent.updatedBy !== typeAgent.createdBy) {
      try {
        const updatedInfo = await this.employeeInfoService.getEmployeeInfo(typeAgent.updatedBy);
        result.updated = {
          employeeId: updatedInfo.id,
          name: updatedInfo.name,
          avatar: updatedInfo.avatar || null,
        };
      } catch (error) {
        this.logger.warn(`Cannot get updated employee info for ID ${typeAgent.updatedBy}`);
      }
    }

    // Lấy thông tin người xóa
    if (typeAgent.deletedBy) {
      try {
        const deletedInfo = await this.employeeInfoService.getEmployeeInfo(typeAgent.deletedBy);
        result.deleted = {
          employeeId: deletedInfo.id,
          name: deletedInfo.name,
          avatar: deletedInfo.avatar || null,
        };
      } catch (error) {
        this.logger.warn(`Cannot get deleted employee info for ID ${typeAgent.deletedBy}`);
      }
    }

    return result;
  }
}
