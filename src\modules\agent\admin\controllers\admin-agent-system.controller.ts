import { ApiResponseDto, PaginatedResult } from '@/common/response';
import { SWAGGER_API_TAGS } from '@common/swagger/swagger.tags';
import { AdminAgentSystemService } from '@modules/agent/admin/services';
import { JwtEmployeeGuard } from '@modules/auth/guards';
import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  ParseUUIDPipe,
  Patch,
  Post,
  Query,
  UseGuards
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiExtraModels,
  ApiResponse,
  ApiOperation,
  ApiTags,
  ApiCreatedResponse,
  ApiParam,
  ApiOkResponse
} from '@nestjs/swagger';
import {
  AgentSystemDetailDto,
  AgentSystemListItemDto,
  AgentSystemQueryDto,
  AgentSystemTrashItemDto,
  CreateAgentSystemDto,
  UpdateAgentSystemDto,
  RestoreAgentSystemDto
} from '../dto/agent-system';
import { ApiErrorResponse } from '@/common/decorators/api-error-response.decorator';
import { AGENT_ERROR_CODES } from '../../exceptions';
import { ErrorCode } from '@/common';
import { CurrentEmployee } from '@/modules/auth/decorators';

/**
 * Controller xử lý các endpoint liên quan đến Agent System cho Admin
 */
@ApiTags(SWAGGER_API_TAGS.ADMIN_AGENT_SYSTEM)
@ApiBearerAuth('JWT-auth')
@UseGuards(JwtEmployeeGuard)
@Controller('admin/agents/system')
@ApiExtraModels(
  AgentSystemListItemDto,
  AgentSystemDetailDto,
  ApiResponseDto,
  PaginatedResult,
)
export class AdminAgentSystemController {
  constructor(
    private readonly adminAgentSystemService: AdminAgentSystemService,
  ) { }

  /**
   * Lấy danh sách agent system với phân trang
   * @param queryDto Tham số truy vấn
   * @returns Danh sách agent system với phân trang
   */
  @Get()
  @ApiOperation({
    summary: 'Lấy danh sách agent system',
    description: 'Lấy danh sách agent system với phân trang và lọc',
  })
  @ApiResponse({
    status: 200,
    description: 'Danh sách agent system',
    schema: ApiResponseDto.getPaginatedSchema(AgentSystemListItemDto)
  })
  @ApiErrorResponse(
    AGENT_ERROR_CODES.AGENT_SYSTEM_NOT_FOUND,
    ErrorCode.INTERNAL_SERVER_ERROR
  )
  async findAll(
    @Query() queryDto: AgentSystemQueryDto,
  ): Promise<ApiResponseDto<PaginatedResult<AgentSystemListItemDto>>> {
    return this.adminAgentSystemService.findAll(queryDto);
  }

  /**
   * Lấy danh sách agent system đã xóa
   * @param queryDto Tham số truy vấn
   * @returns Danh sách agent system đã xóa với phân trang
   */
  @Get('trash')
  @ApiOperation({
    summary: 'Lấy danh sách agent system đã xóa',
    description: 'Lấy danh sách agent system đã xóa với phân trang và tìm kiếm'
  })
  @ApiOkResponse({
    description: 'Danh sách agent system đã xóa',
    type: ApiResponseDto,
  })
  @ApiErrorResponse(
    AGENT_ERROR_CODES.AGENT_SYSTEM_NOT_FOUND,
    ErrorCode.INTERNAL_SERVER_ERROR
  )
  async getDeletedAgentSystems(
    @Query() queryDto: AgentSystemQueryDto,
  ): Promise<ApiResponseDto<PaginatedResult<AgentSystemTrashItemDto>>> {
    const result = await this.adminAgentSystemService.getDeletedAgentSystems(queryDto);
    return ApiResponseDto.success(result);
  }

  /**
   * Lấy thông tin chi tiết agent system theo ID
   * @param id ID của agent system
   * @returns Thông tin chi tiết agent system
   */
  @Get(':id')
  @ApiOperation({
    summary: 'Lấy chi tiết agent system',
    description: 'Lấy thông tin chi tiết agent system theo ID',
  })
  @ApiParam({
    name: 'id',
    description: 'ID của agent system',
    type: String,
  })
  @ApiOkResponse({
    description: 'Thông tin chi tiết agent system',
    type: ApiResponseDto,
  })
  @ApiErrorResponse(
    AGENT_ERROR_CODES.AGENT_SYSTEM_NOT_FOUND,
    ErrorCode.INTERNAL_SERVER_ERROR
  )
  async findOne(
    @Param('id', ParseUUIDPipe) id: string,
  ): Promise<ApiResponseDto<AgentSystemDetailDto>> {
    const result = await this.adminAgentSystemService.findById(id);
    return ApiResponseDto.success(result);
  }

  /**
   * Tạo agent system mới
   * @param createDto Dữ liệu tạo agent system
   * @param employeeId ID của nhân viên tạo
   * @returns ID của agent system đã tạo và URL tải lên avatar (nếu có)
   */
  @Post()
  @ApiOperation({
    summary: 'Tạo agent system mới',
    description: 'Tạo agent system mới với thông tin cung cấp',
  })
  @ApiCreatedResponse({
    description: 'Agent system đã được tạo thành công',
    type: ApiResponseDto,
  })
  @ApiErrorResponse(
    AGENT_ERROR_CODES.AGENT_SYSTEM_NAME_EXISTS,
    AGENT_ERROR_CODES.AGENT_SYSTEM_NAME_CODE_EXISTS,
    AGENT_ERROR_CODES.MODEL_NOT_FOUND,
    AGENT_ERROR_CODES.VECTOR_STORE_NOT_FOUND,
    ErrorCode.INTERNAL_SERVER_ERROR
  )
  async create(
    @Body() createDto: CreateAgentSystemDto,
    @CurrentEmployee('id') employeeId: number,
  ): Promise<ApiResponseDto<{ id: string; avatarUrlUpload?: string }>> {
    const result = await this.adminAgentSystemService.create(
      createDto,
      employeeId,
    );
    return ApiResponseDto.success(result, 'Tạo agent system thành công');
  }

  /**
   * Cập nhật thông tin agent system
   * @param id ID của agent system
   * @param updateDto Dữ liệu cập nhật
   * @param employeeId ID của nhân viên cập nhật
   * @returns URL tải lên avatar mới (nếu có)
   */
  @Patch(':id')
  @ApiOperation({
    summary: 'Cập nhật agent system',
    description: 'Cập nhật thông tin agent system theo ID',
  })
  @ApiParam({
    name: 'id',
    description: 'ID của agent system',
    type: String,
  })
  @ApiOkResponse({
    description: 'Agent system đã được cập nhật thành công',
    type: ApiResponseDto,
  })
  @ApiErrorResponse(
    AGENT_ERROR_CODES.AGENT_SYSTEM_NOT_FOUND,
    AGENT_ERROR_CODES.AGENT_SYSTEM_NAME_EXISTS,
    AGENT_ERROR_CODES.AGENT_SYSTEM_NAME_CODE_EXISTS,
    AGENT_ERROR_CODES.MODEL_NOT_FOUND,
    AGENT_ERROR_CODES.VECTOR_STORE_NOT_FOUND,
    ErrorCode.INTERNAL_SERVER_ERROR
  )
  async update(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateDto: UpdateAgentSystemDto,
    @CurrentEmployee('id') employeeId: number,
  ): Promise<ApiResponseDto<{ id: string; avatarUrlUpload?: string }>> {
    const result = await this.adminAgentSystemService.update(
      id,
      updateDto,
      employeeId,
    );
    return ApiResponseDto.success(result, 'Cập nhật agent system thành công');
  }

  /**
   * Xóa agent system (soft delete)
   * @param id ID của agent system
   * @param employeeId ID của nhân viên xóa
   */
  @Delete(':id')
  @ApiOperation({
    summary: 'Xóa agent system',
    description: 'Xóa agent system theo ID (soft delete)',
  })
  @ApiParam({
    name: 'id',
    description: 'ID của agent system',
    type: String,
  })
  @ApiOkResponse({
    description: 'Agent system đã được xóa thành công',
    type: ApiResponseDto,
  })
  @ApiErrorResponse(
    AGENT_ERROR_CODES.AGENT_SYSTEM_NOT_FOUND,
    ErrorCode.INTERNAL_SERVER_ERROR
  )
  async remove(
    @Param('id', ParseUUIDPipe) id: string,
    @CurrentEmployee('id') employeeId: number,
  ): Promise<ApiResponseDto<{ id: string }>> {
    const result = await this.adminAgentSystemService.remove(id, employeeId);
    return ApiResponseDto.success(result, 'Xóa agent system thành công');
  }

    /**
   * Xóa agent system (soft delete)
   * @param id ID của agent system
   * @param employeeId ID của nhân viên xóa
   */
  @Delete()
  @ApiOperation({
    summary: 'Xóa agent system',
    description: 'Xóa agent system theo ID (soft delete)',
  })
  @ApiOkResponse({
    description: 'Agent system đã được xóa thành công',
    type: ApiResponseDto,
  })
  @ApiErrorResponse(
    AGENT_ERROR_CODES.AGENT_SYSTEM_NOT_FOUND,
    ErrorCode.INTERNAL_SERVER_ERROR
  )
  async removes(
    @Body() ids: string[],
    @CurrentEmployee('id') employeeId: number,
  ): Promise<ApiResponseDto<{ deletedIds: string[]; errorIds: string[] }>> {
    const result = await this.adminAgentSystemService.removes(ids, employeeId);
    return ApiResponseDto.success(result, 'Xóa agent system thành công');
  }



  /**
   * Khôi phục agent system đã xóa
   * @param restoreDto DTO chứa danh sách ID cần khôi phục
   * @param employeeId ID của nhân viên thực hiện khôi phục
   * @returns Thông báo khôi phục thành công
   */
  @Patch('restore')
  @ApiOperation({
    summary: 'Khôi phục agent system đã xóa',
    description: 'Khôi phục agent system đã xóa về trạng thái bình thường'
  })
  @ApiOkResponse({
    description: 'Agent system đã được khôi phục thành công',
    type: ApiResponseDto,
  })
  @ApiErrorResponse(
    AGENT_ERROR_CODES.AGENT_SYSTEM_NOT_FOUND,
    AGENT_ERROR_CODES.AGENT_UPDATE_FAILED,
    ErrorCode.INTERNAL_SERVER_ERROR
  )
  async restoreAgentSystem(
    @Body() restoreDto: RestoreAgentSystemDto,
    @CurrentEmployee('id') employeeId: number,
  ): Promise<ApiResponseDto<null>> {
    await this.adminAgentSystemService.restoreAgentSystem(restoreDto.ids, employeeId);
    return ApiResponseDto.success(null, 'Khôi phục agent system thành công');
  }

  /**
   * Toggle trạng thái active của agent system
   * @param id ID của agent system
   * @returns Trạng thái active mới
   */
  @Patch(':id/toggle-active')
  @ApiOperation({
    summary: 'Toggle trạng thái active của agent system',
    description: 'Tự động đảo ngược trạng thái active của agent system (true ↔ false)',
  })
  @ApiParam({
    name: 'id',
    description: 'ID của agent system',
    type: String,
  })
  @ApiOkResponse({
    description: 'Trạng thái active đã được thay đổi thành công',
    type: ApiResponseDto,
  })
  @ApiErrorResponse(
    AGENT_ERROR_CODES.AGENT_SYSTEM_NOT_FOUND,
    AGENT_ERROR_CODES.AGENT_UPDATE_FAILED,
    ErrorCode.INTERNAL_SERVER_ERROR
  )
  async toggleActiveStatus(
    @Param('id', ParseUUIDPipe) id: string,
  ): Promise<ApiResponseDto<{ id: string; active: boolean }>> {
    const result = await this.adminAgentSystemService.toggleActiveStatus(id);
    return ApiResponseDto.success(result, `Trạng thái active đã được ${result.active ? 'bật' : 'tắt'}`);
  }

  /**
   * Set agent system làm supervisor
   * @param id ID của agent system
   * @returns Thông báo thành công
   */
  @Patch(':id/set-supervisor')
  @ApiOperation({
    summary: 'Set agent system làm supervisor',
    description: 'Set agent system làm supervisor (chỉ 1 supervisor duy nhất trong hệ thống)',
  })
  @ApiParam({
    name: 'id',
    description: 'ID của agent system',
    type: String,
  })
  @ApiOkResponse({
    description: 'Agent system đã được set làm supervisor thành công',
    type: ApiResponseDto,
  })
  @ApiErrorResponse(
    AGENT_ERROR_CODES.AGENT_SYSTEM_NOT_FOUND,
    AGENT_ERROR_CODES.AGENT_UPDATE_FAILED,
    ErrorCode.INTERNAL_SERVER_ERROR
  )
  async setSupervisor(
    @Param('id', ParseUUIDPipe) id: string,
  ): Promise<ApiResponseDto<{ id: string; isSupervisor: boolean }>> {
    const result = await this.adminAgentSystemService.setSupervisor(id);
    return ApiResponseDto.success(result, 'Agent system đã được set làm supervisor thành công');
  }
}
