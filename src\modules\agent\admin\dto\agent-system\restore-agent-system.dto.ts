import { ApiProperty } from '@nestjs/swagger';
import { ArrayMinSize, IsArray, IsNotEmpty, IsUUID } from 'class-validator';

/**
 * DTO cho việc khôi phục agent system
 */
export class RestoreAgentSystemDto {
  /**
   * Danh sách ID của các agent system cần khôi phục
   */
  @ApiProperty({
    description: 'Danh sách ID của các agent system cần khôi phục',
    type: [String],
    example: ['550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************'],
    isArray: true,
    items: {
      type: 'string',
      format: 'uuid'
    }
  })
  @IsArray({ message: 'IDs phải là một mảng' })
  @IsUUID('4', { each: true, message: 'Mỗi ID phải là UUID hợp lệ' })
  @ArrayMinSize(1, { message: '<PERSON><PERSON><PERSON> có ít nhất 1 ID để khôi phục' })
  @IsNotEmpty({ message: '<PERSON><PERSON> sách IDs không được rỗng' })
  ids: string[];
}
