import { ApiProperty } from '@nestjs/swagger';
import { ArrayMinSize, IsArray, IsNotEmpty, IsUUID } from 'class-validator';

/**
 * DTO cho việc khôi phục agent system
 */
export class RestoreAgentSystemDto {
  /**
   * Danh sách ID của các agent system cần khôi phục
   */
  @ApiProperty({
    description: 'Danh sách ID của các agent system cần khôi phục',
    type: [String],
    example: ['550e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************'],
  })
  @IsArray()
  @IsUUID('4', { each: true })
  @ArrayMinSize(1)
  @IsNotEmpty()
  ids: string[];
}
