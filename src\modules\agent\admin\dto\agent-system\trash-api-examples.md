# Agent System Trash Management API Examples

## DELETE /admin/agent-systems/:id - Xóa một agent system

### Request
```http
DELETE /admin/agent-systems/550e8400-e29b-41d4-a716-446655440000
Authorization: Bearer <JWT_TOKEN>
```

### Response Success (200)
```json
{
  "success": true,
  "message": "Xóa agent system thành công",
  "data": {
    "id": "550e8400-e29b-41d4-a716-446655440000"
  }
}
```

### Response Error (404)
```json
{
  "success": false,
  "message": "Agent system không tồn tại",
  "errorCode": "AGENT_SYSTEM_NOT_FOUND",
  "data": null
}
```

## DELETE /admin/agent-systems - Xóa nhiều agent systems

### Request
```http
DELETE /admin/agent-systems
Authorization: Bearer <JWT_TOKEN>
Content-Type: application/json

[
  "550e8400-e29b-41d4-a716-446655440000",
  "550e8400-e29b-41d4-a716-446655440001",
  "550e8400-e29b-41d4-a716-446655440002"
]
```

### Response Success (200)
```json
{
  "success": true,
  "message": "Xóa agent system thành công",
  "data": {
    "deletedIds": [
      "550e8400-e29b-41d4-a716-446655440000",
      "550e8400-e29b-41d4-a716-446655440001"
    ],
    "errorIds": [
      "550e8400-e29b-41d4-a716-446655440002"
    ]
  }
}
```

## GET /admin/agent-systems/trash - Lấy danh sách đã xóa

### Request
```http
GET /admin/agent-systems/trash?page=1&limit=10&search=assistant&sortBy=deletedAt&sortDirection=DESC
Authorization: Bearer <JWT_TOKEN>
```

### Response Success (200)
```json
{
  "success": true,
  "message": "Success",
  "data": {
    "items": [
      {
        "id": "550e8400-e29b-41d4-a716-446655440000",
        "name": "System Assistant",
        "nameCode": "system_assistant",
        "avatar": "https://cdn.example.com/agent-avatars/system-assistant.png",
        "model": "gpt-4o",
        "provider": "OPENAI",
        "active": true,
        "deleted": {
          "employeeId": 1,
          "name": "Nguyễn Văn A",
          "avatar": "https://cdn.example.com/employee-avatars/avatar1.png"
        }
      },
      {
        "id": "550e8400-e29b-41d4-a716-446655440001",
        "name": "Chat Assistant",
        "nameCode": "chat_assistant",
        "avatar": null,
        "model": "claude-3-sonnet",
        "provider": "ANTHROPIC",
        "active": false,
        "deleted": {
          "employeeId": 2,
          "name": "Trần Thị B",
          "avatar": null
        }
      }
    ],
    "meta": {
      "currentPage": 1,
      "itemsPerPage": 10,
      "totalItems": 2,
      "totalPages": 1,
      "itemCount": 2
    }
  }
}
```

## PATCH /admin/agent-systems/restore - Khôi phục agent systems

### Request
```http
PATCH /admin/agent-systems/restore
Authorization: Bearer <JWT_TOKEN>
Content-Type: application/json

[
  "550e8400-e29b-41d4-a716-446655440000",
  "550e8400-e29b-41d4-a716-446655440001"
]
```

### Response Success (200)
```json
{
  "success": true,
  "message": "Khôi phục agent system thành công",
  "data": null
}
```

### Response Error (400)
```json
{
  "success": false,
  "message": "Danh sách ID không được để trống",
  "errorCode": "AGENT_SYSTEM_NOT_FOUND",
  "data": null
}
```

## Test Cases

### 1. Test xóa agent system thành công
- **Input**: Valid agent system ID
- **Expected**: Trả về ID đã xóa, agent system được soft delete
- **Validation**: 
  - `agents.deletedAt` được set
  - `agents_system.deletedBy` được set với employee ID

### 2. Test xóa agent system không tồn tại
- **Input**: Invalid agent system ID
- **Expected**: Trả về lỗi AGENT_SYSTEM_NOT_FOUND
- **Validation**: Status code 404

### 3. Test xóa nhiều agent systems
- **Input**: Array of agent system IDs (một số valid, một số invalid)
- **Expected**: Trả về danh sách deletedIds và errorIds
- **Validation**: 
  - Valid IDs được xóa thành công
  - Invalid IDs được thêm vào errorIds

### 4. Test lấy danh sách đã xóa với pagination
- **Input**: Query parameters (page, limit, search, sort)
- **Expected**: Trả về danh sách agent systems đã xóa với pagination
- **Validation**: 
  - Chỉ hiển thị agent systems đã xóa
  - Pagination hoạt động đúng
  - Search và sort hoạt động đúng

### 5. Test lấy danh sách đã xóa với search
- **Input**: search="assistant"
- **Expected**: Chỉ trả về agent systems có tên hoặc nameCode chứa "assistant"
- **Validation**: Kết quả search chính xác

### 6. Test khôi phục agent systems thành công
- **Input**: Array of deleted agent system IDs
- **Expected**: Agent systems được khôi phục về trạng thái bình thường
- **Validation**: 
  - `agents.deletedAt` được set về null
  - `agents_system.deletedBy` được set về null

### 7. Test khôi phục với danh sách rỗng
- **Input**: Empty array []
- **Expected**: Trả về lỗi validation
- **Validation**: Status code 400

### 8. Test khôi phục agent system không tồn tại hoặc chưa xóa
- **Input**: ID của agent system chưa xóa hoặc không tồn tại
- **Expected**: Operation tiếp tục với các ID khác, log warning
- **Validation**: Không throw error, chỉ log warning

## Error Codes Used

- `AGENT_SYSTEM_NOT_FOUND`: Agent system không tồn tại
- `AGENT_DELETE_FAILED`: Lỗi khi xóa agent system
- `AGENT_FETCH_FAILED`: Lỗi khi lấy danh sách agent systems
- `AGENT_UPDATE_FAILED`: Lỗi khi khôi phục agent system

## Database Changes

### Soft Delete Process
1. **agents table**: Set `deletedAt` = current timestamp
2. **agents_system table**: Set `deletedBy` = employee ID

### Restore Process
1. **agents table**: Set `deletedAt` = null
2. **agents_system table**: Set `deletedBy` = null

## Performance Considerations

1. **Bulk Operations**: Xử lý từng ID một cách tuần tự để đảm bảo transaction safety
2. **Employee Info**: Cache employee information để tránh N+1 queries
3. **Pagination**: Sử dụng offset/limit cho performance tốt
4. **Indexing**: Đảm bảo có index trên `deletedAt` và `deletedBy` columns
