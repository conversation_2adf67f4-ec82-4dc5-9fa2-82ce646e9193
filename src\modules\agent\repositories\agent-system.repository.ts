import { Injectable, Logger } from '@nestjs/common';
import { DataSource, Repository, SelectQueryBuilder } from 'typeorm';
import { AgentSystem } from '@modules/agent/entities';

/**
 * Repository cho AgentSystem
 * Xử lý các thao tác CRUD và truy vấn nâng cao liên quan đến agent hệ thống
 */
@Injectable()
export class AgentSystemRepository extends Repository<AgentSystem> {
  private readonly logger = new Logger(AgentSystemRepository.name);

  constructor(private dataSource: DataSource) {
    super(AgentSystem, dataSource.createEntityManager());
  }

  /**
   * Tạo query builder cơ bản cho AgentSystem
   * @returns SelectQueryBuilder cho AgentSystem
   */
  private createBaseQuery(): SelectQueryBuilder<AgentSystem> {
    return this.createQueryBuilder('agentSystem');
  }

  /**
   * Tìm agent hệ thống theo ID
   * @param id ID của agent hệ thống
   * @returns AgentSystem nếu tìm thấy, null nếu không tìm thấy
   */
  async findById(id: string): Promise<AgentSystem | null> {
    return this.createBaseQuery()
      .where('agentSystem.id = :id', { id })
      .andWhere('agentSystem.deletedBy IS NULL')
      .getOne();
  }

  /**
   * Tìm agent hệ thống theo mã định danh
   * @param nameCode Mã định danh của agent hệ thống
   * @returns AgentSystem nếu tìm thấy, null nếu không tìm thấy
   */
  async findByNameCode(nameCode: string): Promise<AgentSystem | null> {
    return this.createBaseQuery()
      .where('agentSystem.nameCode = :nameCode', { nameCode })
      .andWhere('agentSystem.deletedBy IS NULL')
      .getOne();
  }

  /**
   * Lấy danh sách agent systems với thông tin từ bảng agents (name, avatar) với phân trang
   * @param page Số trang
   * @param limit Số lượng item trên một trang
   * @param search Từ khóa tìm kiếm
   * @param active Trạng thái hoạt động (tùy chọn)
   * @param sortBy Trường sắp xếp
   * @param sortDirection Hướng sắp xếp
   * @returns Danh sách agent systems với thông tin chi tiết
   */
  async findPaginatedWithAgentInfo(
    page: number,
    limit: number,
    search?: string,
    active?: boolean,
    sortBy: string = 'nameCode',
    sortDirection: 'ASC' | 'DESC' = 'ASC',
  ): Promise<{ items: any[]; total: number }> {
    const qb = this.createQueryBuilder('agentSystem')
      .leftJoin('agents', 'agent', 'agentSystem.id = agent.id')
      .leftJoin('system_models', 'systemModel', 'agentSystem.systemModelId = systemModel.id')
      .select([
        'agentSystem.id AS id',
        'agentSystem.nameCode AS nameCode',
        'agentSystem.description AS description',
        'agentSystem.isSupervisor AS isSupervisor',
        'agentSystem.active AS active',
        'agentSystem.systemModelId AS systemModelId',
        'agent.name AS name',
        'agent.avatar AS avatar',
        'systemModel.modelId AS modelId',
        'systemModel.active AS active',
        'systemModel.provider AS provider'
      ])
      .where('agentSystem.deletedBy IS NULL')
      .andWhere('agent.deletedAt IS NULL');

    // Thêm điều kiện tìm kiếm nếu có
    if (search) {
      qb.andWhere(
        '(agentSystem.nameCode ILIKE :search OR agentSystem.description ILIKE :search OR agent.name ILIKE :search)',
        { search: `%${search}%` }
      );
    }

    // Thêm điều kiện lọc theo trạng thái hoạt động
    if (active !== undefined) {
      qb.andWhere('agentSystem.active = :active', { active });
    }

    // Thêm sắp xếp
    if (sortBy === 'name') {
      qb.orderBy('agent.name', sortDirection);
    } else if (sortBy === 'nameCode') {
      qb.orderBy('agentSystem.nameCode', sortDirection);
    } else if (sortBy === 'active') {
      qb.orderBy('agentSystem.active', sortDirection);
    } else {
      qb.orderBy('agentSystem.nameCode', sortDirection);
    }

    // Thêm phân trang
    qb.skip((page - 1) * limit).take(limit);

    const [rawResults, total] = await Promise.all([
      qb.getRawMany(),
      qb.getCount()
    ]);

    return {
      items: rawResults,
      total,
    };
  }

  /**
   * Lấy danh sách agent systems với phân trang (method cũ để backward compatibility)
   * @param page Số trang
   * @param limit Số lượng item trên một trang
   * @param search Từ khóa tìm kiếm
   * @param active Trạng thái hoạt động (tùy chọn)
   * @param sortBy Trường sắp xếp
   * @param sortDirection Hướng sắp xếp
   * @returns Danh sách agent systems với phân trang
   */
  async findPaginated(
    page: number,
    limit: number,
    search?: string,
    active?: boolean,
    sortBy: string = 'nameCode',
    sortDirection: 'ASC' | 'DESC' = 'ASC',
  ): Promise<{ items: AgentSystem[]; total: number }> {
    const qb = this.createBaseQuery()
      .where('agentSystem.deletedBy IS NULL');

    // Thêm điều kiện tìm kiếm nếu có
    if (search) {
      qb.andWhere('(agentSystem.nameCode ILIKE :search OR agentSystem.description ILIKE :search)', {
        search: `%${search}%`,
      });
    }

    // Thêm điều kiện lọc theo trạng thái hoạt động
    if (active !== undefined) {
      qb.andWhere('agentSystem.active = :active', { active });
    }

    // Thêm sắp xếp và phân trang
    qb.orderBy(`agentSystem.${sortBy}`, sortDirection)
      .skip((page - 1) * limit)
      .take(limit);

    const [items, total] = await qb.getManyAndCount();

    return {
      items,
      total,
    };
  }

  /**
   * Lấy danh sách agent system đã xóa với phân trang
   * @param page Số trang
   * @param limit Số lượng item trên một trang
   * @param search Từ khóa tìm kiếm
   * @param sortBy Trường sắp xếp
   * @param sortDirection Hướng sắp xếp
   * @returns Danh sách agent system đã xóa với phân trang
   */
  async findDeletedPaginated(
    page: number,
    limit: number,
    search?: string,
    sortBy: string = 'deletedBy',
    sortDirection: 'ASC' | 'DESC' = 'DESC',
  ): Promise<{ items: AgentSystem[]; total: number; deletedEmployees?: any[] }> {
    // Query chính để lấy agent systems đã xóa
    const qb = this.createBaseQuery()
      .where('agentSystem.deletedBy IS NOT NULL');

    // Thêm điều kiện tìm kiếm nếu có (tìm theo nameCode)
    if (search) {
      qb.andWhere('agentSystem.nameCode ILIKE :search', {
        search: `%${search}%`,
      });
    }

    // Thêm sắp xếp
    qb.orderBy(`agentSystem.nameCode`, sortDirection);

    // Thêm phân trang
    qb.skip((page - 1) * limit).take(limit);

    // Lấy danh sách agent systems đã xóa
    const [items, total] = await qb.getManyAndCount();

    // Lấy thông tin employees đã xóa (batch query)
    const deletedEmployeeIds = [...new Set(items.map(item => item.deletedBy).filter(Boolean))];
    let deletedEmployees: any[] = [];

    if (deletedEmployeeIds.length > 0) {
      const employeeQb = this.dataSource.createQueryBuilder()
        .select([
          'employee.id',
          'employee.fullName',
          'employee.avatar'
        ])
        .from('employees', 'employee')
        .where('employee.id IN (:...ids)', { ids: deletedEmployeeIds });

      const employeeResults = await employeeQb.getRawMany();

      // Map employee info với agent systems
      deletedEmployees = items.map(agentSystem => {
        const employee = employeeResults.find(emp => emp.employee_id === agentSystem.deletedBy);
        return {
          agentSystemId: agentSystem.id,
          employeeId: agentSystem.deletedBy,
          employeeName: employee?.employee_fullName || 'Unknown',
          employeeAvatar: employee?.employee_avatar || null,
        };
      });
    }

    return {
      items,
      total,
      deletedEmployees,
    };
  }

  /**
   * Khôi phục agent system đã xóa
   * @param id ID của agent system cần khôi phục
   * @param employeeId ID của nhân viên thực hiện khôi phục
   * @returns true nếu khôi phục thành công
   */
  async restoreAgentSystem(id: string, employeeId: number): Promise<boolean> {
    try {
      // Cập nhật thông tin khôi phục
      const result = await this.createQueryBuilder()
        .update(AgentSystem)
        .set({
          deletedBy: () => 'NULL',
          updatedBy: employeeId,
        })
        .where('id = :id', { id })
        .andWhere('deletedBy IS NOT NULL')
        .execute();

      if (result.affected && result.affected > 0) {
        this.logger.debug(`Đã khôi phục agent system với ID ${id}`);
        return true;
      }

      return false;
    } catch (error) {
      this.logger.error(`Lỗi khi khôi phục agent system ${id}: ${error.message}`, error.stack);
      return false;
    }
  }

  /**
   * Xóa mềm agent system
   * @param id ID của agent system cần xóa
   * @param employeeId ID của nhân viên thực hiện xóa
   * @returns true nếu xóa thành công
   */
  async softDeleteCustom(id: string, employeeId: number): Promise<boolean> {
    try {
      const result = await this.createQueryBuilder()
        .update(AgentSystem)
        .set({
          deletedBy: employeeId,
          updatedBy: employeeId,
        })
        .where('id = :id', { id })
        .andWhere('deletedBy IS NULL')
        .execute();

      return result.affected !== null && result.affected !== undefined && result.affected > 0;
    } catch (error) {
      this.logger.error(`Lỗi khi xóa mềm agent system ${id}: ${error.message}`);
      return false;
    }
  }

  /**
   * Cập nhật trạng thái hoạt động của agent system
   * @param id ID của agent system
   * @param active Trạng thái hoạt động mới
   * @param employeeId ID của nhân viên thực hiện cập nhật
   * @returns true nếu cập nhật thành công
   */
  async updateActiveStatus(id: string, active: boolean, employeeId: number): Promise<boolean> {
    try {
      const result = await this.createQueryBuilder()
        .update(AgentSystem)
        .set({
          active,
          updatedBy: employeeId,
        })
        .where('id = :id', { id })
        .andWhere('deletedBy IS NULL')
        .execute();

      return result.affected !== null && result.affected !== undefined && result.affected > 0;
    } catch (error) {
      this.logger.error(`Lỗi khi cập nhật trạng thái agent system ${id}: ${error.message}`);
      return false;
    }
  }

  /**
   * Lấy danh sách agent systems theo system model ID
   * @param systemModelId ID của system model
   * @returns Danh sách agent systems
   */
  async findBySystemModelId(systemModelId: string): Promise<AgentSystem[]> {
    return this.createBaseQuery()
      .where('agentSystem.systemModelId = :systemModelId', { systemModelId })
      .andWhere('agentSystem.deletedBy IS NULL')
      .orderBy('agentSystem.nameCode', 'ASC')
      .getMany();
  }

  /**
   * Lấy danh sách supervisor agents
   * @returns Danh sách supervisor agents
   */
  async findSupervisors(): Promise<AgentSystem[]> {
    return this.createBaseQuery()
      .where('agentSystem.isSupervisor = :isSupervisor', { isSupervisor: true })
      .andWhere('agentSystem.deletedBy IS NULL')
      .andWhere('agentSystem.active = :active', { active: true })
      .orderBy('agentSystem.nameCode', 'ASC')
      .getMany();
  }

  /**
   * Tìm các agent systems tồn tại và chưa bị xóa (bulk operation)
   * @param ids Danh sách IDs cần kiểm tra
   * @returns Danh sách IDs hợp lệ
   */
  async findExistingIds(ids: string[]): Promise<string[]> {
    if (!ids || ids.length === 0) {
      return [];
    }

    const results = await this.createQueryBuilder('as')
      .leftJoin('agents', 'a', 'as.id = a.id')
      .select(['as.id'])
      .where('as.id IN (:...ids)', { ids })
      .andWhere('as.deletedBy IS NULL')
      .andWhere('a.deletedAt IS NULL')
      .getRawMany();

    return results.map(item => item.as_id);
  }

  /**
   * Bulk soft delete agent systems
   * @param ids Danh sách IDs cần xóa
   * @param employeeId ID của nhân viên thực hiện xóa
   * @returns Số lượng records đã được cập nhật
   */
  async bulkSoftDelete(ids: string[], employeeId: number): Promise<number> {
    if (!ids || ids.length === 0) {
      return 0;
    }

    const result = await this.createQueryBuilder()
      .update(AgentSystem)
      .set({
        deletedBy: employeeId,
        updatedBy: employeeId
      })
      .where('id IN (:...ids)', { ids })
      .andWhere('deletedBy IS NULL')
      .execute();

    return result.affected || 0;
  }

  /**
   * Tìm các agent systems đã xóa (bulk operation)
   * @param ids Danh sách IDs cần kiểm tra
   * @returns Danh sách IDs đã xóa và có thể khôi phục
   */
  async findDeletedIds(ids: string[]): Promise<string[]> {
    if (!ids || ids.length === 0) {
      return [];
    }

    const results = await this.createQueryBuilder('as')
      .leftJoin('agents', 'a', 'as.id = a.id')
      .select(['as.id'])
      .where('as.id IN (:...ids)', { ids })
      .andWhere('as.deletedBy IS NOT NULL')
      .andWhere('a.deletedAt IS NOT NULL')
      .getRawMany();

    return results.map(item => item.as_id);
  }

  /**
   * Bulk restore agent systems
   * @param ids Danh sách IDs cần khôi phục
   * @returns Số lượng records đã được khôi phục
   */
  async bulkRestore(ids: string[]): Promise<number> {
    if (!ids || ids.length === 0) {
      return 0;
    }

    const result = await this.createQueryBuilder()
      .update(AgentSystem)
      .set({ deletedBy: null })
      .where('id IN (:...ids)', { ids })
      .andWhere('deletedBy IS NOT NULL')
      .execute();

    return result.affected || 0;
  }

  /**
   * Lấy danh sách agent systems đã xóa với thông tin chi tiết và phân trang
   * @param page Số trang
   * @param limit Số lượng item trên một trang
   * @param search Từ khóa tìm kiếm
   * @param sortBy Trường sắp xếp
   * @param sortDirection Hướng sắp xếp
   * @returns Danh sách agent systems đã xóa với thông tin chi tiết
   */
  async findDeletedWithDetailsPaginated(
    page: number,
    limit: number,
    search?: string,
    sortBy: string = 'deletedAt',
    sortDirection: 'ASC' | 'DESC' = 'DESC',
  ): Promise<{
    items: any[];
    total: number;
    deletedByIds: number[];
  }> {
    // Query chính để lấy agent systems đã xóa với thông tin liên quan
    const queryBuilder = this.createQueryBuilder('as')
      .leftJoin('agents', 'a', 'as.id = a.id')
      .leftJoin('system_models', 'sm', 'as.systemModelId = sm.id')
      .select([
        'as.id as id',
        'as.nameCode as nameCode',
        'as.description as description',
        'as.active as active',
        'as.deletedBy as deletedBy',
        'a.name as name',
        'a.avatar as avatar',
        'a.deletedAt as deletedAt',
        'sm.modelId as modelId',
        'sm.provider as provider'
      ])
      .where('as.deletedBy IS NOT NULL')
      .andWhere('a.deletedAt IS NOT NULL');

    // Thêm search nếu có
    if (search) {
      queryBuilder.andWhere(
        '(a.name ILIKE :search OR as.nameCode ILIKE :search OR as.description ILIKE :search)',
        { search: `%${search}%` }
      );
    }

    // Thêm sorting
    const sortField = sortBy === 'name' ? 'a.name' :
                     sortBy === 'nameCode' ? 'as.nameCode' :
                     'a.deletedAt'; // Default sort by deletedAt

    queryBuilder.orderBy(sortField, sortDirection);

    // Pagination
    const offset = (page - 1) * limit;
    queryBuilder.skip(offset).take(limit);

    // Lấy dữ liệu
    const rawItems = await queryBuilder.getRawMany();

    // Query riêng để đếm tổng số (không có pagination)
    const totalQuery = this.createQueryBuilder('as')
      .leftJoin('agents', 'a', 'as.id = a.id')
      .where('as.deletedBy IS NOT NULL')
      .andWhere('a.deletedAt IS NOT NULL');

    if (search) {
      totalQuery.andWhere(
        '(a.name ILIKE :search OR as.nameCode ILIKE :search OR as.description ILIKE :search)',
        { search: `%${search}%` }
      );
    }

    const total = await totalQuery.getCount();

    // Lấy danh sách unique deletedBy IDs
    const deletedByIds = [...new Set(rawItems.map((item: any) => item.deletedBy).filter(Boolean))];

    return {
      items: rawItems,
      total,
      deletedByIds
    };
  }

  /**
   * Lấy thông tin employees theo danh sách IDs
   * @param employeeIds Danh sách employee IDs
   * @returns Map với key là employeeId và value là thông tin employee
   */
  async getEmployeesInfoMap(employeeIds: number[]): Promise<Map<number, any>> {
    const employeeInfoMap = new Map();

    if (!employeeIds || employeeIds.length === 0) {
      return employeeInfoMap;
    }

    try {
      // Query để lấy thông tin employees
      const employeeQb = this.dataSource.createQueryBuilder()
        .select([
          'employee.id as id',
          'employee.fullName as fullName',
          'employee.avatar as avatar'
        ])
        .from('employees', 'employee')
        .where('employee.id IN (:...ids)', { ids: employeeIds });

      const employeeResults = await employeeQb.getRawMany();

      // Tạo map từ kết quả
      employeeResults.forEach(emp => {
        employeeInfoMap.set(emp.id, {
          employeeId: emp.id,
          name: emp.fullName || 'Unknown',
          avatar: emp.avatar || null
        });
      });

    } catch (error) {
      this.logger.error(`Error fetching employee info: ${error.message}`, error.stack);
    }

    return employeeInfoMap;
  }

  /**
   * Toggle trạng thái active của agent system
   * @param id ID của agent system
   * @returns Trạng thái active mới
   */
  async toggleActiveStatus(id: string): Promise<boolean> {
    // Lấy trạng thái hiện tại
    const agentSystem = await this.findOne({ where: { id } });
    if (!agentSystem) {
      return false;
    }

    // Đảo ngược trạng thái
    const newActiveStatus = !agentSystem.active;

    // Cập nhật database
    await this.createQueryBuilder()
      .update(AgentSystem)
      .set({ active: newActiveStatus })
      .where('id = :id', { id })
      .execute();

    return newActiveStatus;
  }

  /**
   * Set agent system làm supervisor (chỉ 1 supervisor duy nhất)
   * @param id ID của agent system được set làm supervisor
   * @returns Số lượng records đã được cập nhật
   */
  async setSupervisor(id: string): Promise<number> {
    // Bước 1: Set tất cả agents khác thành không phải supervisor
    await this.createQueryBuilder()
      .update(AgentSystem)
      .set({ isSupervisor: false })
      .where('deletedBy IS NULL')
      .execute();

    // Bước 2: Set agent được chọn thành supervisor
    const result = await this.createQueryBuilder()
      .update(AgentSystem)
      .set({ isSupervisor: true })
      .where('id = :id', { id })
      .andWhere('deletedBy IS NULL')
      .execute();

    return result.affected || 0;
  }

  /**
   * Kiểm tra agent system có tồn tại và chưa bị xóa không
   * @param id ID của agent system
   * @returns true nếu tồn tại, false nếu không
   */
  async existsAndNotDeleted(id: string): Promise<boolean> {
    const count = await this.createQueryBuilder('as')
      .where('as.id = :id', { id })
      .andWhere('as.deletedBy IS NULL')
      .getCount();

    return count > 0;
  }
}
